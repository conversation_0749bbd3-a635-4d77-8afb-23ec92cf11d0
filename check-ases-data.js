const { Pool } = require('pg');

// إعداد قاعدة البيانات
const pool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'agent',
    password: 'yemen123',
    port: 5432,
});

async function checkAsesData() {
    try {
        console.log('فحص بيانات الوكيل "أسس"...');
        
        // التحقق من وجود الوكيل في جدول الوكلاء
        const agentResult = await pool.query(
            'SELECT agent_id, agent_name FROM agents WHERE agent_name = $1',
            ['أسس']
        );
        
        if (agentResult.rows.length === 0) {
            console.log('❌ الوكيل "أسس" غير موجود في جدول الوكلاء');
            return;
        }
        
        const agentId = agentResult.rows[0].agent_id;
        console.log(`✓ الوكيل "أسس" موجود - المعرف: ${agentId}`);
        
        // فحص بيانات البوابة
        const gatewayResult = await pool.query(
            'SELECT COUNT(*) as count, SUM(amount) as total FROM gateway_collections WHERE agent_id = $1',
            [agentId]
        );
        
        console.log(`بيانات البوابة: ${gatewayResult.rows[0].count} سجل، الإجمالي: ${gatewayResult.rows[0].total || 0}`);
        
        // فحص بيانات ريال موبايل
        const ryalResult = await pool.query(
            'SELECT COUNT(*) as count, SUM(amount) as total FROM ryal_mobile WHERE agent_id = $1',
            [agentId]
        );
        
        console.log(`بيانات ريال موبايل: ${ryalResult.rows[0].count} سجل، الإجمالي: ${ryalResult.rows[0].total || 0}`);
        
        // عرض بعض البيانات كعينة
        if (gatewayResult.rows[0].count > 0) {
            console.log('\nعينة من بيانات البوابة:');
            const sampleGateway = await pool.query(
                'SELECT date_id, amount FROM gateway_collections WHERE agent_id = $1 ORDER BY date_id LIMIT 5',
                [agentId]
            );
            sampleGateway.rows.forEach(row => {
                console.log(`  ${row.date_id}: ${row.amount}`);
            });
        }
        
        if (ryalResult.rows[0].count > 0) {
            console.log('\nعينة من بيانات ريال موبايل:');
            const sampleRyal = await pool.query(
                'SELECT date_id, amount FROM ryal_mobile WHERE agent_id = $1 ORDER BY date_id LIMIT 5',
                [agentId]
            );
            sampleRyal.rows.forEach(row => {
                console.log(`  ${row.date_id}: ${row.amount}`);
            });
        }
        
        // فحص جميع الوكلاء للمقارنة
        console.log('\n--- فحص جميع الوكلاء ---');
        const allAgents = await pool.query('SELECT agent_id, agent_name FROM agents ORDER BY agent_name');
        
        for (const agent of allAgents.rows) {
            const gatewayCount = await pool.query(
                'SELECT COUNT(*) as count FROM gateway_collections WHERE agent_id = $1',
                [agent.agent_id]
            );
            const ryalCount = await pool.query(
                'SELECT COUNT(*) as count FROM ryal_mobile WHERE agent_id = $1',
                [agent.agent_id]
            );
            
            console.log(`${agent.agent_name}: البوابة=${gatewayCount.rows[0].count} سجل، ريال=${ryalCount.rows[0].count} سجل`);
        }
        
    } catch (err) {
        console.error('خطأ في فحص البيانات:', err);
    } finally {
        await pool.end();
    }
}

// تشغيل الفحص
checkAsesData();
