const { Pool } = require('pg');

// إعداد قاعدة البيانات
const pool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'agent',
    password: 'yemen123',
    port: 5432,
});

// دالة تنسيق التاريخ (عرض التاريخ فقط)
function formatDateOnly(date) {
    if (!date) return null;
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

async function testDateFormat() {
    try {
        console.log('اختبار تنسيق التاريخ...');
        
        // اختبار التقرير المفصل
        const result = await pool.query(`
            SELECT 
                d.date_id,
                a.agent_name,
                COALESCE(gc.amount, 0) as gateway_amount,
                COALESCE(rm.amount, 0) as ryal_amount
            FROM days d
            CROSS JOIN agents a
            LEFT JOIN gateway_collections gc ON a.agent_id = gc.agent_id AND gc.date_id = d.date_id
            LEFT JOIN ryal_mobile rm ON a.agent_id = rm.agent_id AND rm.date_id = d.date_id
            WHERE d.date_id BETWEEN $1 AND $2 AND a.agent_name = 'أبو اسامه'
            ORDER BY d.date_id, a.agent_name
            LIMIT 5
        `, ['2025-07-13', '2025-07-14']);
        
        console.log('البيانات الأصلية من قاعدة البيانات:');
        result.rows.forEach(row => {
            console.log(`التاريخ الأصلي: ${row.date_id}`);
            console.log(`التاريخ المنسق: ${formatDateOnly(row.date_id)}`);
            console.log('---');
        });
        
        // تنسيق التاريخ في النتائج
        const formattedResults = result.rows.map(row => ({
            ...row,
            date_id: formatDateOnly(row.date_id)
        }));
        
        console.log('البيانات بعد التنسيق:');
        formattedResults.forEach(row => {
            console.log(`التاريخ المنسق: ${row.date_id}, الوكيل: ${row.agent_name}, البوابة: ${row.gateway_amount}, ريال: ${row.ryal_amount}`);
        });
        
    } catch (err) {
        console.error('خطأ في اختبار التاريخ:', err);
    } finally {
        await pool.end();
    }
}

// تشغيل الاختبار
testDateFormat();
