<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الوكلاء والتحصيلات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#"><i class="fas fa-users-cog me-2"></i>نظام إدارة الوكلاء والتحصيلات</a>
            <div class="navbar-nav ms-auto">
                <button class="btn btn-outline-light" onclick="showSection('agents')">
                    <i class="fas fa-users me-1"></i>الوكلاء
                </button>
                <button class="btn btn-outline-light ms-2" onclick="showSection('users')">
                    <i class="fas fa-user-shield me-1"></i>المستخدمين
                </button>
                <button class="btn btn-outline-light ms-2" onclick="showSection('collections')">
                    <i class="fas fa-money-bill-wave me-1"></i>التحصيلات
                </button>
                <button class="btn btn-outline-light ms-2" onclick="showSection('reports')">
                    <i class="fas fa-chart-bar me-1"></i>التقارير
                </button>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <!-- قسم الوكلاء -->
        <div id="agents-section" class="section">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-users me-2"></i>إدارة الوكلاء</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" id="agentName" class="form-control" placeholder="اسم الوكيل">
                                <button class="btn btn-primary" onclick="addAgent()">
                                    <i class="fas fa-plus me-1"></i>إضافة
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الوكيل</th>
                                    <th>اسم الوكيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="agentsTable">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم المستخدمين -->
        <div id="users-section" class="section" style="display: none;">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-user-shield me-2"></i>إدارة المستخدمين</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <input type="text" id="employeeName" class="form-control" placeholder="اسم الموظف">
                        </div>
                        <div class="col-md-3">
                            <input type="text" id="username" class="form-control" placeholder="اسم الدخول">
                        </div>
                        <div class="col-md-3">
                            <input type="password" id="password" class="form-control" placeholder="كلمة المرور">
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary w-100" onclick="addUser()">
                                <i class="fas fa-plus me-1"></i>إضافة مستخدم
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم المستخدم</th>
                                    <th>اسم الموظف</th>
                                    <th>اسم الدخول</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="usersTable">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم التحصيلات -->
        <div id="collections-section" class="section" style="display: none;">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5><i class="fas fa-money-bill-wave me-2"></i>إدخال التحصيلات</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label">التاريخ:</label>
                            <input type="date" id="collectionDate" class="form-control" onchange="loadCollections()">
                        </div>
                        <div class="col-md-8 text-end">
                            <div class="btn-group-collections">
                                <button class="btn btn-primary" id="addNewBtn" onclick="addNewCollection()" style="display: none;">
                                    <i class="fas fa-plus-circle"></i>إضافة جديد
                                </button>
                                <button class="btn btn-warning" id="editBtn" onclick="enableEdit()" style="display: none;">
                                    <i class="fas fa-edit"></i>تعديل
                                </button>
                                <button class="btn btn-success" id="saveBtn" onclick="saveCollections()" style="display: none;">
                                    <i class="fas fa-save"></i>حفظ التحصيلات
                                </button>
                                <button class="btn btn-secondary" id="cancelBtn" onclick="cancelEdit()" style="display: none;">
                                    <i class="fas fa-times"></i>إلغاء
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>اسم الوكيل</th>
                                    <th>البوابة</th>
                                    <th>ريال موبايل</th>
                                    <th>الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody id="collectionsTable">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم التقارير -->
        <div id="reports-section" class="section" style="display: none;">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5><i class="fas fa-chart-bar me-2"></i>التقارير</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- التقرير اليومي -->
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6><i class="fas fa-calendar-day me-1"></i>التقرير اليومي</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">التاريخ:</label>
                                        <input type="date" id="dailyReportDate" class="form-control">
                                    </div>
                                    <button class="btn btn-primary w-100" onclick="generateDailyReport()">
                                        <i class="fas fa-search me-1"></i>عرض التقرير
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- تقرير الفترة -->
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0"><i class="fas fa-calendar-alt me-1"></i>تقرير حسب الفترة</h6>
                                    <div class="form-check form-check-reverse">
                                        <input class="form-check-input" type="checkbox" id="summaryReport">
                                        <label class="form-check-label text-white fw-bold" for="summaryReport">
                                            تقرير إجمالي
                                        </label>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label class="form-label">من:</label>
                                            <input type="date" id="fromDate" class="form-control">
                                        </div>
                                        <div class="col-6">
                                            <label class="form-label">إلى:</label>
                                            <input type="date" id="toDate" class="form-control">
                                        </div>
                                    </div>
                                    <div class="alert alert-info py-2">
                                        <small>
                                            <i class="fas fa-info-circle me-1"></i>
                                            <span id="reportTypeInfo">تقرير تفصيلي: عرض البيانات يوم بيوم</span>
                                        </small>
                                    </div>
                                    <button class="btn btn-success w-100" onclick="generatePeriodReport()">
                                        <i class="fas fa-search me-1"></i>عرض التقرير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- منطقة عرض التقارير -->
                    <div id="reportResults" class="mt-4" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h6 id="reportTitle"></h6>
                            </div>
                            <div class="card-body">
                                <div id="reportContent"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal للتعديل -->
    <div class="modal fade" id="editModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalTitle">تعديل</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="editModalBody">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveEditBtn">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
