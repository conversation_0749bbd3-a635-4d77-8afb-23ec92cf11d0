<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة الوكلاء والتحصيلات</title>
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💰</text></svg>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="style.css" rel="stylesheet">
</head>
<body>
    <!-- صفحة تسجيل الدخول -->
    <div id="loginPage" class="login-page">
        <div class="login-container">
            <div class="login-card">
                <div class="login-header">
                    <div class="login-logo">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <h2>نظام إدارة الوكلاء والتحصيلات</h2>
                    <p>مرحباً بك، يرجى تسجيل الدخول للمتابعة</p>
                </div>

                <form id="loginForm" class="login-form">
                    <div class="form-group">
                        <label for="loginUsername">
                            <i class="fas fa-user"></i>
                            اسم المستخدم
                        </label>
                        <input type="text" id="loginUsername" class="form-control" placeholder="أدخل اسم المستخدم" required>
                    </div>

                    <div class="form-group">
                        <label for="loginPassword">
                            <i class="fas fa-lock"></i>
                            كلمة المرور
                        </label>
                        <div class="password-input">
                            <input type="password" id="loginPassword" class="form-control" placeholder="أدخل كلمة المرور" required>
                            <button type="button" class="password-toggle" onclick="togglePassword()">
                                <i class="fas fa-eye" id="passwordIcon"></i>
                            </button>
                        </div>
                    </div>

                    <button type="submit" class="btn-login">
                        <i class="fas fa-sign-in-alt"></i>
                        تسجيل الدخول
                    </button>
                </form>

                <div id="loginError" class="login-error" style="display: none;">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span id="loginErrorMessage">خطأ في تسجيل الدخول</span>
                </div>

                <div class="login-footer">
                    <p>&copy; 2025 نظام إدارة الوكلاء والتحصيلات. جميع الحقوق محفوظة.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- التطبيق الرئيسي -->
    <div id="mainApp" style="display: none;">
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="#"><i class="fas fa-users-cog me-2"></i>نظام إدارة الوكلاء والتحصيلات</a>
            <div class="navbar-nav ms-auto">
                <button class="btn btn-outline-light" onclick="showSection('dashboard')">
                    <i class="fas fa-tachometer-alt me-1"></i>لوحة التحكم
                </button>
                <button class="btn btn-outline-light ms-2" onclick="showSection('agents')">
                    <i class="fas fa-users me-1"></i>الوكلاء
                </button>
                <button class="btn btn-outline-light ms-2" onclick="showSection('users')">
                    <i class="fas fa-user-shield me-1"></i>المستخدمين
                </button>
                <button class="btn btn-outline-light ms-2" onclick="showSection('collections')">
                    <i class="fas fa-money-bill-wave me-1"></i>التحصيلات
                </button>
                <button class="btn btn-outline-light ms-2" onclick="showSection('reports')">
                    <i class="fas fa-chart-bar me-1"></i>التقارير
                </button>
                <div class="navbar-text me-3 ms-3">
                    <i class="fas fa-user-circle me-1"></i>
                    <span id="currentUserName">المستخدم</span>
                </div>
                <div class="navbar-text me-3" id="sessionTimer" style="display: none;">
                    <i class="fas fa-clock me-1"></i>
                    <span id="sessionTimeLeft">15:00</span>
                </div>
                <button class="btn btn-outline-light" onclick="logout()">
                    <i class="fas fa-sign-out-alt me-1"></i>تسجيل الخروج
                </button>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-3">
        <!-- لوحة التحكم -->
        <div id="dashboard-section" class="section">
            <!-- إحصائيات سريعة -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">إجمالي الوكلاء</h6>
                                    <h3 id="totalAgents">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-users fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">تحصيلات الأمس</h6>
                                    <h3 id="yesterdayTotal">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-money-bill-wave fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">تحصيلات الأسبوع</h6>
                                    <h3 id="weekTotal">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar-week fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card bg-warning text-white">
                        <div class="card-body">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h6 class="card-title">تحصيلات الشهر</h6>
                                    <h3 id="monthTotal">-</h3>
                                </div>
                                <div class="align-self-center">
                                    <i class="fas fa-calendar-alt fa-2x"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الرسوم البيانية والتحليلات -->
            <div class="row mb-4">
                <!-- أفضل الوكلاء -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5><i class="fas fa-trophy me-2"></i>أفضل الوكلاء للأسبوع الماضي</h5>
                        </div>
                        <div class="card-body">
                            <div id="topAgentsChart" style="height: 300px;">
                                <div class="d-flex justify-content-center align-items-center h-100">
                                    <div class="text-center">
                                        <div class="spinner-border text-primary mb-2" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                        <div class="text-muted">جاري تحميل أفضل الوكلاء للأسبوع الماضي...</div>
                                    </div>
                                </div>
                            </div>
                            <!-- إجمالي الأسبوع الماضي -->
                            <div class="mt-3">
                                <div class="alert alert-primary text-center">
                                    <h6 class="mb-1">إجمالي الأسبوع الماضي</h6>
                                    <h4 id="lastWeekTotal" class="mb-0">-</h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- اتجاه التحصيلات -->
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5><i class="fas fa-chart-line me-2"></i>اتجاه التحصيلات للأسبوع الحالي</h5>
                        </div>
                        <div class="card-body">
                            <div id="weeklyTrendChart" style="height: 300px;">
                                <div class="d-flex justify-content-center align-items-center h-100">
                                    <div class="text-center">
                                        <div class="spinner-border text-success mb-2" role="status">
                                            <span class="visually-hidden">جاري التحميل...</span>
                                        </div>
                                        <div class="text-muted">جاري تحميل اتجاه التحصيلات للأسبوع الحالي...</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- تفاصيل الوكلاء -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                            <h5><i class="fas fa-users me-2"></i>أداء الوكلاء</h5>
                            <div>
                                <button class="btn btn-light btn-sm" onclick="refreshDashboard()">
                                    <i class="fas fa-sync-alt me-1"></i>تحديث
                                </button>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>الوكيل</th>
                                            <th>تحصيلات الأمس</th>
                                            <th>تحصيلات الأسبوع</th>
                                            <th>تحصيلات الشهر</th>
                                            <th>المتوسط اليومي</th>
                                            <th>الحالة</th>
                                        </tr>
                                    </thead>
                                    <tbody id="agentsPerformanceTable">
                                        <tr>
                                            <td colspan="6" class="text-center">
                                                <div class="spinner-border text-primary" role="status">
                                                    <span class="visually-hidden">جاري التحميل...</span>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم الوكلاء -->
        <div id="agents-section" class="section">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-users me-2"></i>إدارة الوكلاء</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <input type="text" id="agentName" class="form-control" placeholder="اسم الوكيل">
                                <button class="btn btn-primary" onclick="addAgent()">
                                    <i class="fas fa-plus me-1"></i>إضافة
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم الوكيل</th>
                                    <th>اسم الوكيل</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="agentsTable">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم المستخدمين -->
        <div id="users-section" class="section" style="display: none;">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5><i class="fas fa-user-shield me-2"></i>إدارة المستخدمين</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-3">
                            <input type="text" id="employeeName" class="form-control" placeholder="اسم الموظف">
                        </div>
                        <div class="col-md-3">
                            <input type="text" id="username" class="form-control" placeholder="اسم الدخول">
                        </div>
                        <div class="col-md-3">
                            <input type="password" id="password" class="form-control" placeholder="كلمة المرور">
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary w-100" onclick="addUser()">
                                <i class="fas fa-plus me-1"></i>إضافة مستخدم
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>رقم المستخدم</th>
                                    <th>اسم الموظف</th>
                                    <th>اسم الدخول</th>
                                    <th>الدور</th>
                                    <th>الصلاحيات</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="usersTable">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم التحصيلات -->
        <div id="collections-section" class="section" style="display: none;">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5><i class="fas fa-money-bill-wave me-2"></i>إدخال التحصيلات</h5>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label class="form-label">التاريخ:</label>
                            <input type="date" id="collectionDate" class="form-control" onchange="loadCollections()">
                        </div>
                        <div class="col-md-8 text-end">
                            <div class="btn-group-collections">
                                <button class="btn btn-primary" id="addCollectionBtn" onclick="addNewCollection()">
                                    <i class="fas fa-plus-circle"></i>إضافة تحصيلات
                                </button>
                                <button class="btn btn-warning" id="editCollectionBtn" onclick="enableEdit()">
                                    <i class="fas fa-edit"></i>تعديل تحصيلات
                                </button>
                                <button class="btn btn-danger" id="deleteCollectionBtn" onclick="enableDelete()">
                                    <i class="fas fa-trash-alt"></i>حذف تحصيلات
                                </button>
                                <button class="btn btn-info" id="importCollectionBtn" onclick="showImportModal()">
                                    <i class="fas fa-file-import"></i>استيراد من نص
                                </button>
                                <button class="btn btn-success" id="saveBtn" onclick="saveCollections()" style="display: none;">
                                    <i class="fas fa-save"></i>حفظ التحصيلات
                                </button>
                                <button class="btn btn-secondary" id="cancelBtn" onclick="cancelEdit()" style="display: none;">
                                    <i class="fas fa-times"></i>إلغاء
                                </button>
                                <button class="btn btn-danger" id="deleteSelectedBtn" onclick="deleteSelected()" style="display: none;">
                                    <i class="fas fa-trash"></i>حذف المحدد
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th style="display: none;" id="selectAllHeader">
                                        <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                        <label for="selectAll" class="text-white ms-1">الكل</label>
                                    </th>
                                    <th>اسم الوكيل</th>
                                    <th>البوابة</th>
                                    <th>ريال موبايل</th>
                                    <th>الإجمالي</th>
                                </tr>
                            </thead>
                            <tbody id="collectionsTable">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- قسم التقارير -->
        <div id="reports-section" class="section" style="display: none;">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5><i class="fas fa-chart-bar me-2"></i>التقارير</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- التقرير اليومي -->
                        <div class="col-md-6">
                            <div class="card border-primary">
                                <div class="card-header bg-primary text-white">
                                    <h6><i class="fas fa-calendar-day me-1"></i>التقرير اليومي</h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label class="form-label">التاريخ:</label>
                                        <input type="date" id="dailyReportDate" class="form-control">
                                    </div>
                                    <button class="btn btn-primary w-100" onclick="generateDailyReport()">
                                        <i class="fas fa-search me-1"></i>عرض التقرير
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- تقرير الفترة -->
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0"><i class="fas fa-calendar-alt me-1"></i>تقرير حسب الفترة</h6>
                                    <div class="form-check form-check-reverse">
                                        <input class="form-check-input" type="checkbox" id="summaryReport">
                                        <label class="form-check-label text-white fw-bold" for="summaryReport">
                                            تقرير إجمالي
                                        </label>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-6">
                                            <label class="form-label">من:</label>
                                            <input type="date" id="fromDate" class="form-control">
                                        </div>
                                        <div class="col-6">
                                            <label class="form-label">إلى:</label>
                                            <input type="date" id="toDate" class="form-control">
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">الوكيل:</label>
                                        <select id="agentSelect" class="form-select">
                                            <option value="">جميع الوكلاء</option>
                                        </select>
                                    </div>
                                    <div class="alert alert-info py-2">
                                        <small>
                                            <i class="fas fa-info-circle me-1"></i>
                                            <span id="reportTypeInfo">تقرير تفصيلي: عرض البيانات يوم بيوم</span>
                                        </small>
                                    </div>
                                    <button class="btn btn-success w-100" onclick="generatePeriodReport()">
                                        <i class="fas fa-search me-1"></i>عرض التقرير
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- منطقة عرض التقارير -->
                    <div id="reportResults" class="mt-4" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h6 id="reportTitle"></h6>
                            </div>
                            <div class="card-body">
                                <div id="reportContent"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal للتعديل -->
    <div class="modal fade" id="editModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="editModalTitle">تعديل</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="editModalBody">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" id="saveEditBtn">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة استيراد التحصيلات -->
    <div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title" id="importModalLabel">
                        <i class="fas fa-file-import me-2"></i>استيراد التحصيلات من نص
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>تعليمات الاستيراد:</strong>
                        <ul class="mb-0 mt-2">
                            <li>كل سطر يحتوي على: <code>اسم الوكيل مبلغ_البوابة مبلغ_ريال_موبايل</code></li>
                            <li>استخدم مسافة واحدة للفصل بين القيم</li>
                            <li>يمكن ترك المبالغ فارغة (0) إذا لم توجد تحصيلات</li>
                            <li>مثال: <code>باتكو 1000000 500000</code></li>
                        </ul>
                    </div>

                    <div class="mb-3">
                        <label for="importText" class="form-label">النص المراد استيراده:</label>
                        <textarea id="importText" class="form-control" rows="10" placeholder="الصق النص هنا...
مثال:
باتكو 1000000 500000
الشرعبي 800000 300000
الاثير 0 400000"></textarea>
                    </div>

                    <div class="mb-3">
                        <button type="button" class="btn btn-secondary" onclick="previewImport()">
                            <i class="fas fa-eye me-1"></i>معاينة البيانات
                        </button>
                    </div>

                    <!-- منطقة المعاينة -->
                    <div id="previewArea" style="display: none;">
                        <h6 class="text-primary">معاينة البيانات:</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>اسم الوكيل</th>
                                        <th>البوابة</th>
                                        <th>ريال موبايل</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody id="previewTable">
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>إلغاء
                    </button>
                    <button type="button" class="btn btn-info" onclick="importCollections()">
                        <i class="fas fa-file-import me-1"></i>استيراد البيانات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إدارة الصلاحيات -->
    <div class="modal fade" id="permissionsModal" tabindex="-1" aria-labelledby="permissionsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="permissionsModalLabel">
                        <i class="fas fa-user-shield me-2"></i>إدارة صلاحيات المستخدم
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>المستخدم:</strong> <span id="permissionsUserName"></span>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label class="form-label">الدور:</label>
                            <select id="userRole" class="form-select" onchange="updateRolePermissions()">
                                <option value="user">مستخدم عادي</option>
                                <option value="admin">مدير</option>
                            </select>
                        </div>
                    </div>

                    <div id="permissionsContainer">
                        <h6 class="text-primary mb-3">الصلاحيات التفصيلية:</h6>

                        <!-- صلاحيات المستخدمين -->
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-users me-2"></i>إدارة المستخدمين</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="users_view">
                                            <label class="form-check-label" for="users_view">مشاهدة</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="users_add">
                                            <label class="form-check-label" for="users_add">إضافة</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="users_edit">
                                            <label class="form-check-label" for="users_edit">تعديل</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="users_delete">
                                            <label class="form-check-label" for="users_delete">حذف</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- صلاحيات الوكلاء -->
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-user-tie me-2"></i>إدارة الوكلاء</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="agents_view">
                                            <label class="form-check-label" for="agents_view">مشاهدة</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="agents_add">
                                            <label class="form-check-label" for="agents_add">إضافة</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="agents_edit">
                                            <label class="form-check-label" for="agents_edit">تعديل</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="agents_delete">
                                            <label class="form-check-label" for="agents_delete">حذف</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- صلاحيات التحصيلات -->
                        <div class="card mb-3">
                            <div class="card-header bg-light">
                                <h6 class="mb-0"><i class="fas fa-money-bill-wave me-2"></i>إدارة التحصيلات</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="collections_view">
                                            <label class="form-check-label" for="collections_view">مشاهدة</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="collections_add">
                                            <label class="form-check-label" for="collections_add">إضافة</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="collections_edit">
                                            <label class="form-check-label" for="collections_edit">تعديل</label>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="collections_delete">
                                            <label class="form-check-label" for="collections_delete">حذف</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>إلغاء
                    </button>
                    <button type="button" class="btn btn-primary" onclick="saveUserPermissions()">
                        <i class="fas fa-save me-1"></i>حفظ الصلاحيات
                    </button>
                </div>
            </div>
        </div>
    </div>

    </div> <!-- إغلاق mainApp -->

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="script.js"></script>
</body>
</html>
