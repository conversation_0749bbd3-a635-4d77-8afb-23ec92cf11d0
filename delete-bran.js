const { Pool } = require('pg');

// إعداد قاعدة البيانات
const pool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'agent',
    password: 'yemen123',
    port: 5432,
});

async function deleteBranAgent() {
    try {
        console.log('بدء حذف الوكيل "بران" وجميع بياناته...');
        
        // الحصول على معرف الوكيل "بران"
        const agentResult = await pool.query(
            'SELECT agent_id FROM agents WHERE agent_name = $1',
            ['بران']
        );
        
        if (agentResult.rows.length === 0) {
            console.log('الوكيل "بران" غير موجود');
            return;
        }
        
        const agentId = agentResult.rows[0].agent_id;
        console.log(`معرف الوكيل "بران": ${agentId}`);
        
        // حذف البيانات من جدول gateway_collections
        const gatewayResult = await pool.query(
            'DELETE FROM gateway_collections WHERE agent_id = $1',
            [agentId]
        );
        console.log(`تم حذف ${gatewayResult.rowCount} سجل من جدول gateway_collections`);
        
        // حذف البيانات من جدول ryal_mobile
        const ryalResult = await pool.query(
            'DELETE FROM ryal_mobile WHERE agent_id = $1',
            [agentId]
        );
        console.log(`تم حذف ${ryalResult.rowCount} سجل من جدول ryal_mobile`);
        
        // حذف الوكيل من جدول agents
        const agentDeleteResult = await pool.query(
            'DELETE FROM agents WHERE agent_id = $1',
            [agentId]
        );
        console.log(`تم حذف ${agentDeleteResult.rowCount} وكيل من جدول agents`);
        
        console.log('✓ تم حذف الوكيل "بران" وجميع بياناته بنجاح!');
        
    } catch (err) {
        console.error('خطأ في حذف الوكيل "بران":', err);
    } finally {
        await pool.end();
    }
}

// تشغيل الحذف
deleteBranAgent();
