const express = require('express');
const { Pool } = require('pg');
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const csv = require('csv-parser');

const app = express();
const PORT = 7445;

// إعداد قاعدة البيانات
const pool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'agent',
    password: 'yemen123',
    port: 5432,
});

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(express.static('public'));

// تهيئة قاعدة البيانات
async function initDatabase() {
    try {
        console.log('التحقق من اتصال قاعدة البيانات...');
        const result = await pool.query('SELECT NOW()');
        console.log('✓ تم الاتصال بقاعدة البيانات بنجاح');

    } catch (err) {
        console.error('خطأ في الاتصال بقاعدة البيانات:', err.message);
    }
}



// API Routes

// إضافة يوم جديد
app.post('/api/days', async (req, res) => {
    try {
        const { date } = req.body;
        await pool.query(
            'INSERT INTO days (date_id) VALUES ($1) ON CONFLICT (date_id) DO NOTHING',
            [date]
        );
        res.json({ message: 'تم إضافة التاريخ بنجاح' });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// الحصول على جميع الوكلاء
app.get('/api/agents', async (req, res) => {
    try {
        const result = await pool.query('SELECT * FROM agents ORDER BY agent_id');
        res.json(result.rows);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// إضافة وكيل جديد
app.post('/api/agents', async (req, res) => {
    try {
        const { agent_name } = req.body;
        const result = await pool.query(
            'INSERT INTO agents (agent_name) VALUES ($1) RETURNING *',
            [agent_name]
        );
        res.json(result.rows[0]);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// تحديث وكيل
app.put('/api/agents/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { agent_name } = req.body;
        const result = await pool.query(
            'UPDATE agents SET agent_name = $1 WHERE agent_id = $2 RETURNING *',
            [agent_name, id]
        );
        res.json(result.rows[0]);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// حذف وكيل
app.delete('/api/agents/:id', async (req, res) => {
    try {
        const { id } = req.params;
        await pool.query('DELETE FROM agents WHERE agent_id = $1', [id]);
        res.json({ message: 'تم حذف الوكيل بنجاح' });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// الحصول على جميع المستخدمين
app.get('/api/users', async (req, res) => {
    try {
        const result = await pool.query('SELECT user_id, employee_name, username FROM users ORDER BY user_id');
        res.json(result.rows);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// إضافة مستخدم جديد
app.post('/api/users', async (req, res) => {
    try {
        const { employee_name, username, password } = req.body;
        const result = await pool.query(
            'INSERT INTO users (employee_name, username, password) VALUES ($1, $2, $3) RETURNING user_id, employee_name, username',
            [employee_name, username, password]
        );
        res.json(result.rows[0]);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// تحديث مستخدم
app.put('/api/users/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { employee_name, username, password } = req.body;
        const result = await pool.query(
            'UPDATE users SET employee_name = $1, username = $2, password = $3 WHERE user_id = $4 RETURNING user_id, employee_name, username',
            [employee_name, username, password, id]
        );
        res.json(result.rows[0]);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// حذف مستخدم
app.delete('/api/users/:id', async (req, res) => {
    try {
        const { id } = req.params;
        await pool.query('DELETE FROM users WHERE user_id = $1', [id]);
        res.json({ message: 'تم حذف المستخدم بنجاح' });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// الحصول على بيانات التحصيلات لتاريخ محدد
app.get('/api/collections/:date', async (req, res) => {
    try {
        const { date } = req.params;
        const result = await pool.query(`
            SELECT
                a.agent_id,
                a.agent_name,
                COALESCE(gc.amount, 0) as gateway_amount,
                COALESCE(rm.amount, 0) as ryal_amount,
                (COALESCE(gc.amount, 0) + COALESCE(rm.amount, 0)) as total_amount
            FROM agents a
            LEFT JOIN gateway_collections gc ON a.agent_id = gc.agent_id AND gc.date_id = $1
            LEFT JOIN ryal_mobile rm ON a.agent_id = rm.agent_id AND rm.date_id = $1
            ORDER BY a.agent_id
        `, [date]);
        res.json(result.rows);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// حفظ التحصيلات
app.post('/api/collections', async (req, res) => {
    try {
        const { date, collections } = req.body;

        // حذف البيانات الموجودة لهذا التاريخ
        await pool.query('DELETE FROM gateway_collections WHERE date_id = $1', [date]);
        await pool.query('DELETE FROM ryal_mobile WHERE date_id = $1', [date]);

        // إدراج البيانات الجديدة
        for (const collection of collections) {
            if (collection.gateway_amount > 0) {
                await pool.query(
                    'INSERT INTO gateway_collections (agent_id, amount, date_id) VALUES ($1, $2, $3)',
                    [collection.agent_id, collection.gateway_amount, date]
                );
            }
            if (collection.ryal_amount > 0) {
                await pool.query(
                    'INSERT INTO ryal_mobile (agent_id, amount, date_id) VALUES ($1, $2, $3)',
                    [collection.agent_id, collection.ryal_amount, date]
                );
            }
        }

        res.json({ message: 'تم حفظ التحصيلات بنجاح' });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// حذف تحصيلات محددة
app.post('/api/collections/delete', async (req, res) => {
    try {
        const { date, agentIds } = req.body;

        // حذف البيانات للوكلاء المحددين في التاريخ المحدد
        for (const agentId of agentIds) {
            await pool.query(
                'DELETE FROM gateway_collections WHERE agent_id = $1 AND date_id = $2',
                [agentId, date]
            );
            await pool.query(
                'DELETE FROM ryal_mobile WHERE agent_id = $1 AND date_id = $2',
                [agentId, date]
            );
        }

        res.json({ message: 'تم حذف التحصيلات المحددة بنجاح' });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// تقرير يومي
app.get('/api/reports/daily/:date', async (req, res) => {
    try {
        const { date } = req.params;
        const result = await pool.query(`
            SELECT
                a.agent_name,
                COALESCE(gc.amount, 0) as gateway_amount,
                COALESCE(rm.amount, 0) as ryal_amount,
                (COALESCE(gc.amount, 0) + COALESCE(rm.amount, 0)) as total_amount
            FROM agents a
            LEFT JOIN gateway_collections gc ON a.agent_id = gc.agent_id AND gc.date_id = $1
            LEFT JOIN ryal_mobile rm ON a.agent_id = rm.agent_id AND rm.date_id = $1
            ORDER BY a.agent_name
        `, [date]);
        res.json(result.rows);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// تقرير حسب الفترة
app.get('/api/reports/period', async (req, res) => {
    try {
        const { from_date, to_date, summary } = req.query;

        if (summary === 'true') {
            // تقرير تجميعي
            const result = await pool.query(`
                SELECT
                    a.agent_name,
                    COALESCE(SUM(gc.amount), 0) as total_gateway,
                    COALESCE(SUM(rm.amount), 0) as total_ryal,
                    (COALESCE(SUM(gc.amount), 0) + COALESCE(SUM(rm.amount), 0)) as grand_total
                FROM agents a
                LEFT JOIN gateway_collections gc ON a.agent_id = gc.agent_id
                    AND gc.date_id BETWEEN $1 AND $2
                LEFT JOIN ryal_mobile rm ON a.agent_id = rm.agent_id
                    AND rm.date_id BETWEEN $1 AND $2
                GROUP BY a.agent_id, a.agent_name
                ORDER BY a.agent_name
            `, [from_date, to_date]);
            res.json(result.rows);
        } else {
            // تقرير مفصل
            const result = await pool.query(`
                SELECT
                    d.date_id,
                    a.agent_name,
                    COALESCE(gc.amount, 0) as gateway_amount,
                    COALESCE(rm.amount, 0) as ryal_amount
                FROM days d
                CROSS JOIN agents a
                LEFT JOIN gateway_collections gc ON a.agent_id = gc.agent_id AND gc.date_id = d.date_id
                LEFT JOIN ryal_mobile rm ON a.agent_id = rm.agent_id AND rm.date_id = d.date_id
                WHERE d.date_id BETWEEN $1 AND $2
                ORDER BY d.date_id, a.agent_name
            `, [from_date, to_date]);
            res.json(result.rows);
        }
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// الصفحة الرئيسية
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// بدء الخادم
app.listen(PORT, '0.0.0.0', async () => {
    console.log(`الخادم يعمل على المنفذ ${PORT}`);
    console.log(`الرابط المحلي: http://localhost:${PORT}`);
    console.log(`الرابط الخارجي: http://***********:${PORT}`);

    // تهيئة قاعدة البيانات
    await initDatabase();
});
