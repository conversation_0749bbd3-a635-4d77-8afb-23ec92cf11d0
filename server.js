const express = require('express');
const { Pool } = require('pg');
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const csv = require('csv-parser');

const app = express();
const PORT = 7445;

// إعداد قاعدة البيانات
const pool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'agent',
    password: 'yemen123',
    port: 5432,
});

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(express.static('public'));

// دالة تنسيق التاريخ (عرض التاريخ فقط)
function formatDateOnly(date) {
    if (!date) return null;
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// دالة لحساب بداية الأسبوع (الثلاثاء)
function getWeekStart(date = new Date()) {
    const d = new Date(date);
    const day = d.getDay(); // 0 = الأحد, 1 = الاثنين, 2 = الثلاثاء, ...

    // حساب عدد الأيام للوصول للثلاثاء السابق
    let daysToSubtract;
    if (day === 0) { // الأحد
        daysToSubtract = 5; // العودة للثلاثاء السابق
    } else if (day === 1) { // الاثنين
        daysToSubtract = 6; // العودة للثلاثاء السابق
    } else { // الثلاثاء إلى السبت
        daysToSubtract = day - 2; // العودة لثلاثاء هذا الأسبوع
    }

    d.setDate(d.getDate() - daysToSubtract);
    return d;
}

// دالة لحساب نهاية الأسبوع (الاثنين)
function getWeekEnd(date = new Date()) {
    const weekStart = getWeekStart(date);
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6); // إضافة 6 أيام للوصول للاثنين
    return weekEnd;
}

// دالة لحساب بداية الأسبوع الماضي
function getLastWeekStart(date = new Date()) {
    const currentWeekStart = getWeekStart(date);
    const lastWeekStart = new Date(currentWeekStart);
    lastWeekStart.setDate(currentWeekStart.getDate() - 7);
    return lastWeekStart;
}

// دالة لحساب نهاية الأسبوع الماضي
function getLastWeekEnd(date = new Date()) {
    const lastWeekStart = getLastWeekStart(date);
    const lastWeekEnd = new Date(lastWeekStart);
    lastWeekEnd.setDate(lastWeekStart.getDate() + 6);
    return lastWeekEnd;
}

// تهيئة قاعدة البيانات
async function initDatabase() {
    try {
        console.log('التحقق من اتصال قاعدة البيانات...');
        const result = await pool.query('SELECT NOW()');
        console.log('✓ تم الاتصال بقاعدة البيانات بنجاح');

    } catch (err) {
        console.error('خطأ في الاتصال بقاعدة البيانات:', err.message);
    }
}



// API Routes

// إضافة يوم جديد
app.post('/api/days', async (req, res) => {
    try {
        const { date } = req.body;
        await pool.query(
            'INSERT INTO days (date_id) VALUES ($1) ON CONFLICT (date_id) DO NOTHING',
            [date]
        );
        res.json({ message: 'تم إضافة التاريخ بنجاح' });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// الحصول على جميع الوكلاء
app.get('/api/agents', async (req, res) => {
    try {
        const result = await pool.query('SELECT * FROM agents ORDER BY agent_id');
        res.json(result.rows);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// إضافة وكيل جديد
app.post('/api/agents', async (req, res) => {
    try {
        const { agent_name } = req.body;
        const result = await pool.query(
            'INSERT INTO agents (agent_name) VALUES ($1) RETURNING *',
            [agent_name]
        );
        res.json(result.rows[0]);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// تحديث وكيل
app.put('/api/agents/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { agent_name } = req.body;
        const result = await pool.query(
            'UPDATE agents SET agent_name = $1 WHERE agent_id = $2 RETURNING *',
            [agent_name, id]
        );
        res.json(result.rows[0]);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// حذف وكيل
app.delete('/api/agents/:id', async (req, res) => {
    try {
        const { id } = req.params;
        await pool.query('DELETE FROM agents WHERE agent_id = $1', [id]);
        res.json({ message: 'تم حذف الوكيل بنجاح' });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// الحصول على جميع المستخدمين مع الصلاحيات
app.get('/api/users', async (req, res) => {
    try {
        const usersResult = await pool.query('SELECT user_id, employee_name, username, role FROM users ORDER BY user_id');

        const users = [];
        for (const user of usersResult.rows) {
            // جلب صلاحيات كل مستخدم
            const permissionsResult = await pool.query(
                'SELECT module_name, can_view, can_add, can_edit, can_delete FROM user_permissions WHERE user_id = $1',
                [user.user_id]
            );

            // تنظيم الصلاحيات
            const permissions = {};
            permissionsResult.rows.forEach(perm => {
                permissions[perm.module_name] = {
                    view: perm.can_view,
                    add: perm.can_add,
                    edit: perm.can_edit,
                    delete: perm.can_delete
                };
            });

            users.push({
                ...user,
                permissions: permissions
            });
        }

        res.json(users);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// إضافة مستخدم جديد
app.post('/api/users', async (req, res) => {
    try {
        const { employee_name, username, password } = req.body;
        const result = await pool.query(
            'INSERT INTO users (employee_name, username, password) VALUES ($1, $2, $3) RETURNING user_id, employee_name, username',
            [employee_name, username, password]
        );
        res.json(result.rows[0]);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// تحديث مستخدم
app.put('/api/users/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { employee_name, username, password } = req.body;
        const result = await pool.query(
            'UPDATE users SET employee_name = $1, username = $2, password = $3 WHERE user_id = $4 RETURNING user_id, employee_name, username',
            [employee_name, username, password, id]
        );
        res.json(result.rows[0]);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// حذف مستخدم
app.delete('/api/users/:id', async (req, res) => {
    try {
        const { id } = req.params;
        await pool.query('DELETE FROM users WHERE user_id = $1', [id]);
        res.json({ message: 'تم حذف المستخدم بنجاح' });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// جلب صلاحيات مستخدم محدد
app.get('/api/users/:id/permissions', async (req, res) => {
    try {
        const { id } = req.params;

        // جلب بيانات المستخدم
        const userResult = await pool.query(
            'SELECT user_id, employee_name, username, role FROM users WHERE user_id = $1',
            [id]
        );

        if (userResult.rows.length === 0) {
            return res.status(404).json({ success: false, message: 'المستخدم غير موجود' });
        }

        // جلب الصلاحيات
        const permissionsResult = await pool.query(
            'SELECT module_name, can_view, can_add, can_edit, can_delete FROM user_permissions WHERE user_id = $1',
            [id]
        );

        // تنظيم الصلاحيات
        const permissions = {};
        permissionsResult.rows.forEach(perm => {
            permissions[perm.module_name] = {
                view: perm.can_view,
                add: perm.can_add,
                edit: perm.can_edit,
                delete: perm.can_delete
            };
        });

        res.json({
            success: true,
            user: userResult.rows[0],
            permissions: permissions
        });
    } catch (err) {
        res.status(500).json({ success: false, error: err.message });
    }
});

// تحديث صلاحيات مستخدم
app.put('/api/users/:id/permissions', async (req, res) => {
    try {
        const { id } = req.params;
        const { role, permissions } = req.body;

        // تحديث دور المستخدم
        await pool.query('UPDATE users SET role = $1 WHERE user_id = $2', [role, id]);

        // حذف الصلاحيات الحالية
        await pool.query('DELETE FROM user_permissions WHERE user_id = $1', [id]);

        // إضافة الصلاحيات الجديدة
        for (const [module, modulePermissions] of Object.entries(permissions)) {
            await pool.query(`
                INSERT INTO user_permissions (user_id, module_name, can_view, can_add, can_edit, can_delete)
                VALUES ($1, $2, $3, $4, $5, $6)
            `, [
                id,
                module,
                modulePermissions.view || false,
                modulePermissions.add || false,
                modulePermissions.edit || false,
                modulePermissions.delete || false
            ]);
        }

        res.json({ success: true, message: 'تم تحديث الصلاحيات بنجاح' });
    } catch (err) {
        res.status(500).json({ success: false, error: err.message });
    }
});

// الحصول على بيانات التحصيلات لتاريخ محدد
app.get('/api/collections/:date', async (req, res) => {
    try {
        const { date } = req.params;
        const result = await pool.query(`
            SELECT
                a.agent_id,
                a.agent_name,
                COALESCE(gc.amount, 0) as gateway_amount,
                COALESCE(rm.amount, 0) as ryal_amount,
                (COALESCE(gc.amount, 0) + COALESCE(rm.amount, 0)) as total_amount
            FROM agents a
            LEFT JOIN gateway_collections gc ON a.agent_id = gc.agent_id AND gc.date_id = $1
            LEFT JOIN ryal_mobile rm ON a.agent_id = rm.agent_id AND rm.date_id = $1
            ORDER BY a.agent_id
        `, [date]);
        res.json(result.rows);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// حفظ التحصيلات
app.post('/api/collections', async (req, res) => {
    try {
        const { date, collections } = req.body;

        // حذف البيانات الموجودة لهذا التاريخ
        await pool.query('DELETE FROM gateway_collections WHERE date_id = $1', [date]);
        await pool.query('DELETE FROM ryal_mobile WHERE date_id = $1', [date]);

        // إدراج البيانات الجديدة
        for (const collection of collections) {
            if (collection.gateway_amount > 0) {
                await pool.query(
                    'INSERT INTO gateway_collections (agent_id, amount, date_id) VALUES ($1, $2, $3)',
                    [collection.agent_id, collection.gateway_amount, date]
                );
            }
            if (collection.ryal_amount > 0) {
                await pool.query(
                    'INSERT INTO ryal_mobile (agent_id, amount, date_id) VALUES ($1, $2, $3)',
                    [collection.agent_id, collection.ryal_amount, date]
                );
            }
        }

        res.json({ message: 'تم حفظ التحصيلات بنجاح' });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// حذف تحصيلات محددة
app.post('/api/collections/delete', async (req, res) => {
    try {
        const { date, agentIds } = req.body;

        // حذف البيانات للوكلاء المحددين في التاريخ المحدد
        for (const agentId of agentIds) {
            await pool.query(
                'DELETE FROM gateway_collections WHERE agent_id = $1 AND date_id = $2',
                [agentId, date]
            );
            await pool.query(
                'DELETE FROM ryal_mobile WHERE agent_id = $1 AND date_id = $2',
                [agentId, date]
            );
        }

        res.json({ message: 'تم حذف التحصيلات المحددة بنجاح' });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// تقرير يومي
app.get('/api/reports/daily/:date', async (req, res) => {
    try {
        const { date } = req.params;
        const result = await pool.query(`
            SELECT
                a.agent_name,
                COALESCE(gc.amount, 0) as gateway_amount,
                COALESCE(rm.amount, 0) as ryal_amount,
                (COALESCE(gc.amount, 0) + COALESCE(rm.amount, 0)) as total_amount
            FROM agents a
            LEFT JOIN gateway_collections gc ON a.agent_id = gc.agent_id AND gc.date_id = $1
            LEFT JOIN ryal_mobile rm ON a.agent_id = rm.agent_id AND rm.date_id = $1
            ORDER BY a.agent_name
        `, [date]);
        res.json(result.rows);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// تقرير حسب الفترة
app.get('/api/reports/period', async (req, res) => {
    try {
        const { from_date, to_date, summary, agent_id } = req.query;

        if (summary === 'true') {
            // تقرير إجمالي - استعلام محسن لتجنب المضاعفة
            let query = `
                SELECT
                    a.agent_name,
                    COALESCE(gateway_totals.total_gateway, 0) as total_gateway,
                    COALESCE(ryal_totals.total_ryal, 0) as total_ryal,
                    (COALESCE(gateway_totals.total_gateway, 0) + COALESCE(ryal_totals.total_ryal, 0)) as grand_total
                FROM agents a
                LEFT JOIN (
                    SELECT agent_id, SUM(amount) as total_gateway
                    FROM gateway_collections
                    WHERE date_id BETWEEN $1 AND $2
                    GROUP BY agent_id
                ) gateway_totals ON a.agent_id = gateway_totals.agent_id
                LEFT JOIN (
                    SELECT agent_id, SUM(amount) as total_ryal
                    FROM ryal_mobile
                    WHERE date_id BETWEEN $1 AND $2
                    GROUP BY agent_id
                ) ryal_totals ON a.agent_id = ryal_totals.agent_id
            `;

            let params = [from_date, to_date];

            // إضافة تصفية الوكيل إذا تم تحديده
            if (agent_id) {
                query += ` WHERE a.agent_id = $3`;
                params.push(agent_id);
            }

            query += ` ORDER BY a.agent_name`;

            const result = await pool.query(query, params);
            res.json(result.rows);
        } else {
            // تقرير مفصل
            let query = `
                SELECT
                    d.date_id,
                    a.agent_id,
                    a.agent_name,
                    COALESCE(gc.amount, 0) as gateway_amount,
                    COALESCE(rm.amount, 0) as ryal_amount
                FROM days d
                CROSS JOIN agents a
                LEFT JOIN gateway_collections gc ON a.agent_id = gc.agent_id AND gc.date_id = d.date_id
                LEFT JOIN ryal_mobile rm ON a.agent_id = rm.agent_id AND rm.date_id = d.date_id
                WHERE d.date_id BETWEEN $1 AND $2
            `;

            let params = [from_date, to_date];

            // إضافة تصفية الوكيل إذا تم تحديده
            if (agent_id) {
                query += ` AND a.agent_id = $3`;
                params.push(agent_id);
            }

            query += ` ORDER BY d.date_id, a.agent_name`;

            const result = await pool.query(query, params);

            // تنسيق التاريخ في النتائج
            const formattedResults = result.rows.map(row => ({
                ...row,
                date_id: formatDateOnly(row.date_id)
            }));

            res.json(formattedResults);
        }
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// الصفحة الرئيسية
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// تسجيل الدخول
app.post('/api/login', async (req, res) => {
    try {
        console.log('محاولة تسجيل دخول:', req.body);
        const { username, password } = req.body;

        if (!username || !password) {
            console.log('بيانات ناقصة - اسم المستخدم أو كلمة المرور فارغة');
            return res.status(400).json({
                success: false,
                message: 'يرجى إدخال اسم المستخدم وكلمة المرور'
            });
        }

        // البحث عن المستخدم في قاعدة البيانات
        console.log('البحث عن المستخدم:', username);
        const result = await pool.query(
            'SELECT user_id, employee_name, username, role FROM users WHERE username = $1 AND password = $2',
            [username, password]
        );

        console.log('نتيجة البحث:', result.rows.length, 'مستخدم');
        if (result.rows.length === 0) {
            console.log('لم يتم العثور على المستخدم أو كلمة المرور خاطئة');
            return res.status(401).json({
                success: false,
                message: 'اسم المستخدم أو كلمة المرور غير صحيحة'
            });
        }

        const user = result.rows[0];

        // جلب صلاحيات المستخدم
        const permissionsResult = await pool.query(
            'SELECT module_name, can_view, can_add, can_edit, can_delete FROM user_permissions WHERE user_id = $1',
            [user.user_id]
        );

        // تنظيم الصلاحيات
        const permissions = {};
        permissionsResult.rows.forEach(perm => {
            permissions[perm.module_name] = {
                view: perm.can_view,
                add: perm.can_add,
                edit: perm.can_edit,
                delete: perm.can_delete
            };
        });

        // إرجاع بيانات المستخدم مع الصلاحيات
        console.log('تم تسجيل الدخول بنجاح للمستخدم:', user.username);
        res.json({
            success: true,
            message: 'تم تسجيل الدخول بنجاح',
            user: {
                user_id: user.user_id,
                employee_name: user.employee_name,
                username: user.username,
                role: user.role,
                permissions: permissions
            }
        });

    } catch (err) {
        console.error('خطأ في تسجيل الدخول:', err);
        res.status(500).json({
            success: false,
            message: 'خطأ في الخادم'
        });
    }
});

// إحصائيات لوحة التحكم
app.get('/api/dashboard/stats', async (req, res) => {
    try {
        const today = new Date().toISOString().split('T')[0];
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const yesterdayStr = yesterday.toISOString().split('T')[0];

        const currentWeekStart = getWeekStart();
        const monthStart = new Date();
        monthStart.setDate(monthStart.getDate() - 30);

        // إجمالي الوكلاء
        const agentsCount = await pool.query('SELECT COUNT(*) as count FROM agents');

        // تحصيلات الأمس
        const yesterdayStats = await pool.query(`
            SELECT
                (COALESCE((SELECT SUM(amount) FROM gateway_collections WHERE date_id = $1), 0) +
                 COALESCE((SELECT SUM(amount) FROM ryal_mobile WHERE date_id = $1), 0)) as total
        `, [yesterdayStr]);

        // تحصيلات الأسبوع الحالي (من الثلاثاء إلى الاثنين)
        // إذا لم توجد بيانات للأسبوع الحالي، استخدم الأسبوع الماضي
        const currentWeekEnd = getWeekEnd();
        const weekStats = await pool.query(`
            SELECT
                (COALESCE((SELECT SUM(amount) FROM gateway_collections WHERE date_id >= $1 AND date_id <= $2), 0) +
                 COALESCE((SELECT SUM(amount) FROM ryal_mobile WHERE date_id >= $1 AND date_id <= $2), 0)) as total
        `, [formatDateOnly(currentWeekStart), formatDateOnly(currentWeekEnd)]);

        // تحصيلات الشهر
        const monthStats = await pool.query(`
            SELECT
                (COALESCE((SELECT SUM(amount) FROM gateway_collections WHERE date_id >= $1), 0) +
                 COALESCE((SELECT SUM(amount) FROM ryal_mobile WHERE date_id >= $1), 0)) as total
        `, [formatDateOnly(monthStart)]);

        res.json({
            totalAgents: agentsCount.rows[0].count,
            yesterdayTotal: parseFloat(yesterdayStats.rows[0].total || 0),
            weekTotal: parseFloat(weekStats.rows[0].total || 0),
            monthTotal: parseFloat(monthStats.rows[0].total || 0)
        });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// أداء الوكلاء
app.get('/api/dashboard/agents-performance', async (req, res) => {
    try {
        const yesterday = new Date();
        yesterday.setDate(yesterday.getDate() - 1);
        const yesterdayStr = yesterday.toISOString().split('T')[0];

        const currentWeekStart = getWeekStart();
        const currentWeekEnd = getWeekEnd();
        const monthStart = new Date();
        monthStart.setDate(monthStart.getDate() - 30);

        const result = await pool.query(`
            SELECT
                a.agent_name,
                -- تحصيلات الأمس
                COALESCE(yesterday_gc.amount, 0) + COALESCE(yesterday_rm.amount, 0) as yesterday_total,
                -- تحصيلات الأسبوع
                COALESCE(week_gc.total, 0) + COALESCE(week_rm.total, 0) as week_total,
                -- تحصيلات الشهر
                COALESCE(month_gc.total, 0) + COALESCE(month_rm.total, 0) as month_total
            FROM agents a
            -- تحصيلات الأمس
            LEFT JOIN gateway_collections yesterday_gc ON a.agent_id = yesterday_gc.agent_id AND yesterday_gc.date_id = $1
            LEFT JOIN ryal_mobile yesterday_rm ON a.agent_id = yesterday_rm.agent_id AND yesterday_rm.date_id = $1
            -- تحصيلات الأسبوع
            LEFT JOIN (
                SELECT agent_id, SUM(amount) as total
                FROM gateway_collections
                WHERE date_id >= $2 AND date_id <= $3
                GROUP BY agent_id
            ) week_gc ON a.agent_id = week_gc.agent_id
            LEFT JOIN (
                SELECT agent_id, SUM(amount) as total
                FROM ryal_mobile
                WHERE date_id >= $2 AND date_id <= $3
                GROUP BY agent_id
            ) week_rm ON a.agent_id = week_rm.agent_id
            -- تحصيلات الشهر
            LEFT JOIN (
                SELECT agent_id, SUM(amount) as total
                FROM gateway_collections
                WHERE date_id >= $4
                GROUP BY agent_id
            ) month_gc ON a.agent_id = month_gc.agent_id
            LEFT JOIN (
                SELECT agent_id, SUM(amount) as total
                FROM ryal_mobile
                WHERE date_id >= $4
                GROUP BY agent_id
            ) month_rm ON a.agent_id = month_rm.agent_id
            ORDER BY week_total DESC
        `, [yesterdayStr, formatDateOnly(currentWeekStart), formatDateOnly(currentWeekEnd), formatDateOnly(monthStart)]);

        res.json(result.rows);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// أفضل الوكلاء للأسبوع الماضي
app.get('/api/dashboard/top-agents', async (req, res) => {
    try {
        const lastWeekStart = getLastWeekStart();
        const lastWeekEnd = getLastWeekEnd();

        const result = await pool.query(`
            SELECT
                a.agent_name,
                COALESCE(gc.total, 0) + COALESCE(rm.total, 0) as week_total
            FROM agents a
            LEFT JOIN (
                SELECT agent_id, SUM(amount) as total
                FROM gateway_collections
                WHERE date_id >= $1 AND date_id <= $2
                GROUP BY agent_id
            ) gc ON a.agent_id = gc.agent_id
            LEFT JOIN (
                SELECT agent_id, SUM(amount) as total
                FROM ryal_mobile
                WHERE date_id >= $1 AND date_id <= $2
                GROUP BY agent_id
            ) rm ON a.agent_id = rm.agent_id
            ORDER BY week_total DESC
            LIMIT 5
        `, [formatDateOnly(lastWeekStart), formatDateOnly(lastWeekEnd)]);

        res.json(result.rows);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// إجمالي الأسبوع الماضي
app.get('/api/dashboard/last-week-total', async (req, res) => {
    try {
        const lastWeekStart = getLastWeekStart();
        const lastWeekEnd = getLastWeekEnd();
        const startStr = formatDateOnly(lastWeekStart);
        const endStr = formatDateOnly(lastWeekEnd);

        console.log('حساب إجمالي الأسبوع الماضي:');
        console.log('من:', startStr, 'إلى:', endStr);

        // استعلام منفصل للبوابة
        const gatewayResult = await pool.query(`
            SELECT COALESCE(SUM(amount), 0) as total
            FROM gateway_collections
            WHERE date_id >= $1 AND date_id <= $2
        `, [startStr, endStr]);

        // استعلام منفصل لريال موبايل
        const ryalResult = await pool.query(`
            SELECT COALESCE(SUM(amount), 0) as total
            FROM ryal_mobile
            WHERE date_id >= $1 AND date_id <= $2
        `, [startStr, endStr]);

        const gatewayTotal = parseFloat(gatewayResult.rows[0].total || 0);
        const ryalTotal = parseFloat(ryalResult.rows[0].total || 0);
        const grandTotal = gatewayTotal + ryalTotal;

        console.log('إجمالي البوابة:', gatewayTotal);
        console.log('إجمالي ريال موبايل:', ryalTotal);
        console.log('الإجمالي الكلي:', grandTotal);

        res.json({
            total: grandTotal,
            gatewayTotal: gatewayTotal,
            ryalTotal: ryalTotal,
            weekStart: startStr,
            weekEnd: endStr
        });
    } catch (err) {
        console.error('خطأ في حساب إجمالي الأسبوع الماضي:', err);
        res.status(500).json({ error: err.message });
    }
});

// اتجاه التحصيلات الأسبوعية (الأسبوع الحالي من الثلاثاء إلى الاثنين)
app.get('/api/dashboard/weekly-trend', async (req, res) => {
    try {
        const currentWeekStart = getWeekStart();
        const currentWeekEnd = getWeekEnd();

        // الحصول على جميع التواريخ في الفترة
        const result = await pool.query(`
            WITH date_range AS (
                SELECT DISTINCT date_id
                FROM (
                    SELECT date_id FROM gateway_collections WHERE date_id >= $1 AND date_id <= $2
                    UNION
                    SELECT date_id FROM ryal_mobile WHERE date_id >= $1 AND date_id <= $2
                ) dates
            )
            SELECT
                dr.date_id,
                (COALESCE((SELECT SUM(amount) FROM gateway_collections gc WHERE gc.date_id = dr.date_id), 0) +
                 COALESCE((SELECT SUM(amount) FROM ryal_mobile rm WHERE rm.date_id = dr.date_id), 0)) as daily_total
            FROM date_range dr
            ORDER BY dr.date_id
        `, [formatDateOnly(currentWeekStart), formatDateOnly(currentWeekEnd)]);

        // تنسيق التاريخ في النتائج
        const formattedResults = result.rows.map(row => ({
            ...row,
            date_id: formatDateOnly(row.date_id)
        }));

        res.json(formattedResults);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// API للتحقق من البيانات المستوردة
app.get('/api/debug/imported-data', async (req, res) => {
    try {
        const { date } = req.query;

        if (!date) {
            return res.status(400).json({ error: 'التاريخ مطلوب' });
        }

        // جلب بيانات البوابة
        const gatewayData = await pool.query(`
            SELECT a.agent_name, gc.amount, gc.date_id
            FROM gateway_collections gc
            JOIN agents a ON gc.agent_id = a.agent_id
            WHERE gc.date_id = $1
            ORDER BY a.agent_name
        `, [date]);

        // جلب بيانات ريال موبايل
        const ryalData = await pool.query(`
            SELECT a.agent_name, rm.amount, rm.date_id
            FROM ryal_mobile rm
            JOIN agents a ON rm.agent_id = a.agent_id
            WHERE rm.date_id = $1
            ORDER BY a.agent_name
        `, [date]);

        res.json({
            date: date,
            gateway: gatewayData.rows,
            ryal: ryalData.rows,
            totalGateway: gatewayData.rows.reduce((sum, row) => sum + parseFloat(row.amount), 0),
            totalRyal: ryalData.rows.reduce((sum, row) => sum + parseFloat(row.amount), 0)
        });
    } catch (err) {
        console.error('خطأ في جلب البيانات المستوردة:', err);
        res.status(500).json({ error: err.message });
    }
});

// بدء الخادم
app.listen(PORT, '0.0.0.0', async () => {
    console.log(`الخادم يعمل على المنفذ ${PORT}`);
    console.log(`الرابط المحلي: http://localhost:${PORT}`);
    console.log('الخادم جاهز لاستقبال طلبات تسجيل الدخول');
    console.log(`الرابط الخارجي: http://***********:${PORT}`);

    // تهيئة قاعدة البيانات
    await initDatabase();
});
