const express = require('express');
const { Pool } = require('pg');
const bodyParser = require('body-parser');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const csv = require('csv-parser');

const app = express();
const PORT = 7445;

// إعداد قاعدة البيانات
const pool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'agent',
    password: 'yemen123',
    port: 5432,
});

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(express.static('public'));

// دالة تنسيق التاريخ (عرض التاريخ فقط)
function formatDateOnly(date) {
    if (!date) return null;
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

// تهيئة قاعدة البيانات
async function initDatabase() {
    try {
        console.log('التحقق من اتصال قاعدة البيانات...');
        const result = await pool.query('SELECT NOW()');
        console.log('✓ تم الاتصال بقاعدة البيانات بنجاح');

    } catch (err) {
        console.error('خطأ في الاتصال بقاعدة البيانات:', err.message);
    }
}



// API Routes

// إضافة يوم جديد
app.post('/api/days', async (req, res) => {
    try {
        const { date } = req.body;
        await pool.query(
            'INSERT INTO days (date_id) VALUES ($1) ON CONFLICT (date_id) DO NOTHING',
            [date]
        );
        res.json({ message: 'تم إضافة التاريخ بنجاح' });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// الحصول على جميع الوكلاء
app.get('/api/agents', async (req, res) => {
    try {
        const result = await pool.query('SELECT * FROM agents ORDER BY agent_id');
        res.json(result.rows);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// إضافة وكيل جديد
app.post('/api/agents', async (req, res) => {
    try {
        const { agent_name } = req.body;
        const result = await pool.query(
            'INSERT INTO agents (agent_name) VALUES ($1) RETURNING *',
            [agent_name]
        );
        res.json(result.rows[0]);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// تحديث وكيل
app.put('/api/agents/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { agent_name } = req.body;
        const result = await pool.query(
            'UPDATE agents SET agent_name = $1 WHERE agent_id = $2 RETURNING *',
            [agent_name, id]
        );
        res.json(result.rows[0]);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// حذف وكيل
app.delete('/api/agents/:id', async (req, res) => {
    try {
        const { id } = req.params;
        await pool.query('DELETE FROM agents WHERE agent_id = $1', [id]);
        res.json({ message: 'تم حذف الوكيل بنجاح' });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// الحصول على جميع المستخدمين
app.get('/api/users', async (req, res) => {
    try {
        const result = await pool.query('SELECT user_id, employee_name, username FROM users ORDER BY user_id');
        res.json(result.rows);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// إضافة مستخدم جديد
app.post('/api/users', async (req, res) => {
    try {
        const { employee_name, username, password } = req.body;
        const result = await pool.query(
            'INSERT INTO users (employee_name, username, password) VALUES ($1, $2, $3) RETURNING user_id, employee_name, username',
            [employee_name, username, password]
        );
        res.json(result.rows[0]);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// تحديث مستخدم
app.put('/api/users/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { employee_name, username, password } = req.body;
        const result = await pool.query(
            'UPDATE users SET employee_name = $1, username = $2, password = $3 WHERE user_id = $4 RETURNING user_id, employee_name, username',
            [employee_name, username, password, id]
        );
        res.json(result.rows[0]);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// حذف مستخدم
app.delete('/api/users/:id', async (req, res) => {
    try {
        const { id } = req.params;
        await pool.query('DELETE FROM users WHERE user_id = $1', [id]);
        res.json({ message: 'تم حذف المستخدم بنجاح' });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// الحصول على بيانات التحصيلات لتاريخ محدد
app.get('/api/collections/:date', async (req, res) => {
    try {
        const { date } = req.params;
        const result = await pool.query(`
            SELECT
                a.agent_id,
                a.agent_name,
                COALESCE(gc.amount, 0) as gateway_amount,
                COALESCE(rm.amount, 0) as ryal_amount,
                (COALESCE(gc.amount, 0) + COALESCE(rm.amount, 0)) as total_amount
            FROM agents a
            LEFT JOIN gateway_collections gc ON a.agent_id = gc.agent_id AND gc.date_id = $1
            LEFT JOIN ryal_mobile rm ON a.agent_id = rm.agent_id AND rm.date_id = $1
            ORDER BY a.agent_id
        `, [date]);
        res.json(result.rows);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// حفظ التحصيلات
app.post('/api/collections', async (req, res) => {
    try {
        const { date, collections } = req.body;

        // حذف البيانات الموجودة لهذا التاريخ
        await pool.query('DELETE FROM gateway_collections WHERE date_id = $1', [date]);
        await pool.query('DELETE FROM ryal_mobile WHERE date_id = $1', [date]);

        // إدراج البيانات الجديدة
        for (const collection of collections) {
            if (collection.gateway_amount > 0) {
                await pool.query(
                    'INSERT INTO gateway_collections (agent_id, amount, date_id) VALUES ($1, $2, $3)',
                    [collection.agent_id, collection.gateway_amount, date]
                );
            }
            if (collection.ryal_amount > 0) {
                await pool.query(
                    'INSERT INTO ryal_mobile (agent_id, amount, date_id) VALUES ($1, $2, $3)',
                    [collection.agent_id, collection.ryal_amount, date]
                );
            }
        }

        res.json({ message: 'تم حفظ التحصيلات بنجاح' });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// حذف تحصيلات محددة
app.post('/api/collections/delete', async (req, res) => {
    try {
        const { date, agentIds } = req.body;

        // حذف البيانات للوكلاء المحددين في التاريخ المحدد
        for (const agentId of agentIds) {
            await pool.query(
                'DELETE FROM gateway_collections WHERE agent_id = $1 AND date_id = $2',
                [agentId, date]
            );
            await pool.query(
                'DELETE FROM ryal_mobile WHERE agent_id = $1 AND date_id = $2',
                [agentId, date]
            );
        }

        res.json({ message: 'تم حذف التحصيلات المحددة بنجاح' });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// تقرير يومي
app.get('/api/reports/daily/:date', async (req, res) => {
    try {
        const { date } = req.params;
        const result = await pool.query(`
            SELECT
                a.agent_name,
                COALESCE(gc.amount, 0) as gateway_amount,
                COALESCE(rm.amount, 0) as ryal_amount,
                (COALESCE(gc.amount, 0) + COALESCE(rm.amount, 0)) as total_amount
            FROM agents a
            LEFT JOIN gateway_collections gc ON a.agent_id = gc.agent_id AND gc.date_id = $1
            LEFT JOIN ryal_mobile rm ON a.agent_id = rm.agent_id AND rm.date_id = $1
            ORDER BY a.agent_name
        `, [date]);
        res.json(result.rows);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// تقرير حسب الفترة
app.get('/api/reports/period', async (req, res) => {
    try {
        const { from_date, to_date, summary } = req.query;

        if (summary === 'true') {
            // تقرير إجمالي - استعلام محسن لتجنب المضاعفة
            const result = await pool.query(`
                SELECT
                    a.agent_name,
                    COALESCE(gateway_totals.total_gateway, 0) as total_gateway,
                    COALESCE(ryal_totals.total_ryal, 0) as total_ryal,
                    (COALESCE(gateway_totals.total_gateway, 0) + COALESCE(ryal_totals.total_ryal, 0)) as grand_total
                FROM agents a
                LEFT JOIN (
                    SELECT agent_id, SUM(amount) as total_gateway
                    FROM gateway_collections
                    WHERE date_id BETWEEN $1 AND $2
                    GROUP BY agent_id
                ) gateway_totals ON a.agent_id = gateway_totals.agent_id
                LEFT JOIN (
                    SELECT agent_id, SUM(amount) as total_ryal
                    FROM ryal_mobile
                    WHERE date_id BETWEEN $1 AND $2
                    GROUP BY agent_id
                ) ryal_totals ON a.agent_id = ryal_totals.agent_id
                ORDER BY a.agent_name
            `, [from_date, to_date]);
            res.json(result.rows);
        } else {
            // تقرير مفصل
            const result = await pool.query(`
                SELECT
                    d.date_id,
                    a.agent_name,
                    COALESCE(gc.amount, 0) as gateway_amount,
                    COALESCE(rm.amount, 0) as ryal_amount
                FROM days d
                CROSS JOIN agents a
                LEFT JOIN gateway_collections gc ON a.agent_id = gc.agent_id AND gc.date_id = d.date_id
                LEFT JOIN ryal_mobile rm ON a.agent_id = rm.agent_id AND rm.date_id = d.date_id
                WHERE d.date_id BETWEEN $1 AND $2
                ORDER BY d.date_id, a.agent_name
            `, [from_date, to_date]);

            // تنسيق التاريخ في النتائج
            const formattedResults = result.rows.map(row => ({
                ...row,
                date_id: formatDateOnly(row.date_id)
            }));

            res.json(formattedResults);
        }
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// الصفحة الرئيسية
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// إحصائيات لوحة التحكم
app.get('/api/dashboard/stats', async (req, res) => {
    try {
        const today = new Date().toISOString().split('T')[0];
        const weekStart = new Date();
        weekStart.setDate(weekStart.getDate() - 7);
        const monthStart = new Date();
        monthStart.setDate(monthStart.getDate() - 30);

        // إجمالي الوكلاء
        const agentsCount = await pool.query('SELECT COUNT(*) as count FROM agents');

        // تحصيلات اليوم
        const todayStats = await pool.query(`
            SELECT
                (COALESCE((SELECT SUM(amount) FROM gateway_collections WHERE date_id = $1), 0) +
                 COALESCE((SELECT SUM(amount) FROM ryal_mobile WHERE date_id = $1), 0)) as total
        `, [today]);

        // تحصيلات الأسبوع
        const weekStats = await pool.query(`
            SELECT
                (COALESCE((SELECT SUM(amount) FROM gateway_collections WHERE date_id >= $1), 0) +
                 COALESCE((SELECT SUM(amount) FROM ryal_mobile WHERE date_id >= $1), 0)) as total
        `, [formatDateOnly(weekStart)]);

        // تحصيلات الشهر
        const monthStats = await pool.query(`
            SELECT
                (COALESCE((SELECT SUM(amount) FROM gateway_collections WHERE date_id >= $1), 0) +
                 COALESCE((SELECT SUM(amount) FROM ryal_mobile WHERE date_id >= $1), 0)) as total
        `, [formatDateOnly(monthStart)]);

        res.json({
            totalAgents: agentsCount.rows[0].count,
            todayTotal: parseFloat(todayStats.rows[0].total || 0),
            weekTotal: parseFloat(weekStats.rows[0].total || 0),
            monthTotal: parseFloat(monthStats.rows[0].total || 0)
        });
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// أداء الوكلاء
app.get('/api/dashboard/agents-performance', async (req, res) => {
    try {
        const today = new Date().toISOString().split('T')[0];
        const weekStart = new Date();
        weekStart.setDate(weekStart.getDate() - 7);
        const monthStart = new Date();
        monthStart.setDate(monthStart.getDate() - 30);

        const result = await pool.query(`
            SELECT
                a.agent_name,
                -- تحصيلات اليوم
                COALESCE(today_gc.amount, 0) + COALESCE(today_rm.amount, 0) as today_total,
                -- تحصيلات الأسبوع
                COALESCE(week_gc.total, 0) + COALESCE(week_rm.total, 0) as week_total,
                -- تحصيلات الشهر
                COALESCE(month_gc.total, 0) + COALESCE(month_rm.total, 0) as month_total
            FROM agents a
            -- تحصيلات اليوم
            LEFT JOIN gateway_collections today_gc ON a.agent_id = today_gc.agent_id AND today_gc.date_id = $1
            LEFT JOIN ryal_mobile today_rm ON a.agent_id = today_rm.agent_id AND today_rm.date_id = $1
            -- تحصيلات الأسبوع
            LEFT JOIN (
                SELECT agent_id, SUM(amount) as total
                FROM gateway_collections
                WHERE date_id >= $2
                GROUP BY agent_id
            ) week_gc ON a.agent_id = week_gc.agent_id
            LEFT JOIN (
                SELECT agent_id, SUM(amount) as total
                FROM ryal_mobile
                WHERE date_id >= $2
                GROUP BY agent_id
            ) week_rm ON a.agent_id = week_rm.agent_id
            -- تحصيلات الشهر
            LEFT JOIN (
                SELECT agent_id, SUM(amount) as total
                FROM gateway_collections
                WHERE date_id >= $3
                GROUP BY agent_id
            ) month_gc ON a.agent_id = month_gc.agent_id
            LEFT JOIN (
                SELECT agent_id, SUM(amount) as total
                FROM ryal_mobile
                WHERE date_id >= $3
                GROUP BY agent_id
            ) month_rm ON a.agent_id = month_rm.agent_id
            ORDER BY week_total DESC
        `, [today, formatDateOnly(weekStart), formatDateOnly(monthStart)]);

        res.json(result.rows);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// أفضل الوكلاء هذا الأسبوع
app.get('/api/dashboard/top-agents', async (req, res) => {
    try {
        const weekStart = new Date();
        weekStart.setDate(weekStart.getDate() - 7);

        const result = await pool.query(`
            SELECT
                a.agent_name,
                COALESCE(gc.total, 0) + COALESCE(rm.total, 0) as week_total
            FROM agents a
            LEFT JOIN (
                SELECT agent_id, SUM(amount) as total
                FROM gateway_collections
                WHERE date_id >= $1
                GROUP BY agent_id
            ) gc ON a.agent_id = gc.agent_id
            LEFT JOIN (
                SELECT agent_id, SUM(amount) as total
                FROM ryal_mobile
                WHERE date_id >= $1
                GROUP BY agent_id
            ) rm ON a.agent_id = rm.agent_id
            ORDER BY week_total DESC
            LIMIT 5
        `, [formatDateOnly(weekStart)]);

        res.json(result.rows);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// اتجاه التحصيلات الأسبوعية
app.get('/api/dashboard/weekly-trend', async (req, res) => {
    try {
        const weekStart = new Date();
        weekStart.setDate(weekStart.getDate() - 7);

        // الحصول على جميع التواريخ في الفترة
        const result = await pool.query(`
            WITH date_range AS (
                SELECT DISTINCT date_id
                FROM (
                    SELECT date_id FROM gateway_collections WHERE date_id >= $1
                    UNION
                    SELECT date_id FROM ryal_mobile WHERE date_id >= $1
                ) dates
            )
            SELECT
                dr.date_id,
                (COALESCE((SELECT SUM(amount) FROM gateway_collections gc WHERE gc.date_id = dr.date_id), 0) +
                 COALESCE((SELECT SUM(amount) FROM ryal_mobile rm WHERE rm.date_id = dr.date_id), 0)) as daily_total
            FROM date_range dr
            ORDER BY dr.date_id
        `, [formatDateOnly(weekStart)]);

        // تنسيق التاريخ في النتائج
        const formattedResults = result.rows.map(row => ({
            ...row,
            date_id: formatDateOnly(row.date_id)
        }));

        res.json(formattedResults);
    } catch (err) {
        res.status(500).json({ error: err.message });
    }
});

// بدء الخادم
app.listen(PORT, '0.0.0.0', async () => {
    console.log(`الخادم يعمل على المنفذ ${PORT}`);
    console.log(`الرابط المحلي: http://localhost:${PORT}`);
    console.log(`الرابط الخارجي: http://***********:${PORT}`);

    // تهيئة قاعدة البيانات
    await initDatabase();
});
