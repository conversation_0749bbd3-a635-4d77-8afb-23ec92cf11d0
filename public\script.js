// متغيرات عامة
let currentEditId = null;
let currentEditType = null;
let isEditMode = false;
let hasData = false;
let isDeleteMode = false;
let currentUser = null;

// === دوال الصلاحيات ===

// فحص صلاحية معينة
function hasPermission(module, action) {
    console.log(`فحص صلاحية ${action} في ${module}`);
    console.log('المستخدم الحالي:', currentUser);

    if (!currentUser || !currentUser.permissions) {
        console.log('لا يوجد مستخدم أو صلاحيات');
        return false;
    }

    // المدير له صلاحيات كاملة
    if (currentUser.role === 'admin') {
        console.log('المستخدم مدير - صلاحيات كاملة');
        return true;
    }

    const modulePermissions = currentUser.permissions[module];
    if (!modulePermissions) {
        console.log(`لا توجد صلاحيات للوحدة ${module}`);
        return false;
    }

    const result = modulePermissions[action] === true;
    console.log(`نتيجة فحص الصلاحية: ${result}`);
    return result;
}

// فحص صلاحية المشاهدة
function canView(module) {
    return hasPermission(module, 'view');
}

// فحص صلاحية الإضافة
function canAdd(module) {
    return hasPermission(module, 'add');
}

// فحص صلاحية التعديل
function canEdit(module) {
    return hasPermission(module, 'edit');
}

// فحص صلاحية الحذف
function canDelete(module) {
    return hasPermission(module, 'delete');
}

// إخفاء/إظهار العناصر حسب الصلاحيات
function updateUIBasedOnPermissions() {
    console.log('تحديث الواجهة حسب الصلاحيات...');
    if (!currentUser) {
        console.log('لا يوجد مستخدم حالي');
        return;
    }

    // إخفاء أزرار التنقل حسب الصلاحيات
    const navButtons = {
        'users': document.querySelector('button[onclick="showSection(\'users\')"]'),
        'agents': document.querySelector('button[onclick="showSection(\'agents\')"]'),
        'collections': document.querySelector('button[onclick="showSection(\'collections\')"]')
    };

    console.log('أزرار التنقل:', navButtons);

    Object.keys(navButtons).forEach(module => {
        const button = navButtons[module];
        if (button) {
            if (canView(module)) {
                button.style.display = 'inline-block';
            } else {
                button.style.display = 'none';
            }
        }
    });

    // إضافة مؤشر الدور
    const userNameElement = document.getElementById('currentUserName');
    if (userNameElement) {
        const roleText = currentUser.role === 'admin' ? ' (مدير)' : ' (مستخدم)';
        userNameElement.textContent = currentUser.employee_name + roleText;
    }

    // إخفاء/إظهار أزرار الإضافة حسب الصلاحيات
    updateAddButtonsVisibility();
}

// فحص الصلاحية قبل تنفيذ إجراء
function checkPermissionAndExecute(module, action, callback, errorMessage = null) {
    if (hasPermission(module, action)) {
        callback();
    } else {
        const defaultMessage = `ليس لديك صلاحية ${getActionName(action)} في ${getModuleName(module)}`;
        alert(errorMessage || defaultMessage);
    }
}

// ترجمة أسماء الإجراءات
function getActionName(action) {
    const actions = {
        'view': 'المشاهدة',
        'add': 'الإضافة',
        'edit': 'التعديل',
        'delete': 'الحذف'
    };
    return actions[action] || action;
}

// ترجمة أسماء الوحدات
function getModuleName(module) {
    const modules = {
        'users': 'المستخدمين',
        'agents': 'الوكلاء',
        'collections': 'التحصيلات'
    };
    return modules[module] || module;
}

// تحديث رؤية أزرار الإضافة
function updateAddButtonsVisibility() {
    // زر إضافة وكيل
    const addAgentBtn = document.querySelector('button[onclick="addAgent()"]');
    if (addAgentBtn) {
        addAgentBtn.style.display = canAdd('agents') ? 'inline-block' : 'none';
    }

    // زر إضافة مستخدم
    const addUserBtn = document.querySelector('button[onclick="addUser()"]');
    if (addUserBtn) {
        addUserBtn.style.display = canAdd('users') ? 'inline-block' : 'none';
    }

    // إخفاء حقول الإدخال إذا لم تكن هناك صلاحية إضافة
    const agentInputGroup = document.getElementById('agentName')?.closest('.input-group');
    if (agentInputGroup) {
        agentInputGroup.style.display = canAdd('agents') ? 'flex' : 'none';
    }

    const userInputs = document.querySelector('#users-section .row.mb-3');
    if (userInputs) {
        userInputs.style.display = canAdd('users') ? 'block' : 'none';
    }
}

// === دوال تسجيل الدخول ===

// تبديل إظهار/إخفاء كلمة المرور
function togglePassword() {
    const passwordInput = document.getElementById('loginPassword');
    const passwordIcon = document.getElementById('passwordIcon');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordIcon.classList.remove('fa-eye');
        passwordIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        passwordIcon.classList.remove('fa-eye-slash');
        passwordIcon.classList.add('fa-eye');
    }
}

// معالجة تسجيل الدخول
async function handleLogin(event) {
    event.preventDefault();

    const username = document.getElementById('loginUsername').value.trim();
    const password = document.getElementById('loginPassword').value.trim();
    const errorDiv = document.getElementById('loginError');
    const errorMessage = document.getElementById('loginErrorMessage');
    const submitButton = event.target.querySelector('button[type="submit"]');

    // إخفاء رسالة الخطأ
    errorDiv.style.display = 'none';

    // التحقق من الحقول
    if (!username || !password) {
        showLoginError('يرجى إدخال اسم المستخدم وكلمة المرور');
        return;
    }

    // تعطيل الزر وإظهار حالة التحميل
    const originalText = submitButton.innerHTML;
    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري تسجيل الدخول...';
    submitButton.disabled = true;

    try {
        const response = await fetch('/api/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });

        const data = await response.json();

        if (data.success) {
            // حفظ بيانات المستخدم
            currentUser = data.user;
            console.log('تم تسجيل الدخول للمستخدم:', currentUser);
            console.log('الصلاحيات:', currentUser.permissions);
            localStorage.setItem('currentUser', JSON.stringify(currentUser));

            // إخفاء صفحة تسجيل الدخول وإظهار التطبيق
            document.getElementById('loginPage').style.display = 'none';
            document.getElementById('mainApp').style.display = 'block';

            // تحديث الواجهة حسب الصلاحيات
            updateUIBasedOnPermissions();

            // عرض لوحة التحكم
            showSection('dashboard');

        } else {
            showLoginError(data.message || 'خطأ في تسجيل الدخول');
        }

    } catch (error) {
        console.error('خطأ في تسجيل الدخول:', error);
        showLoginError('خطأ في الاتصال بالخادم');
    } finally {
        // إعادة تفعيل الزر
        submitButton.innerHTML = originalText;
        submitButton.disabled = false;
    }
}

// إظهار رسالة خطأ تسجيل الدخول
function showLoginError(message) {
    const errorDiv = document.getElementById('loginError');
    const errorMessage = document.getElementById('loginErrorMessage');

    errorMessage.textContent = message;
    errorDiv.style.display = 'block';

    // إخفاء الرسالة بعد 5 ثوان
    setTimeout(() => {
        errorDiv.style.display = 'none';
    }, 5000);
}

// تسجيل الخروج
function logout() {
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        // مسح بيانات المستخدم
        currentUser = null;
        localStorage.removeItem('currentUser');

        // إخفاء التطبيق وإظهار صفحة تسجيل الدخول
        document.getElementById('mainApp').style.display = 'none';
        document.getElementById('loginPage').style.display = 'block';

        // مسح الحقول
        document.getElementById('loginUsername').value = '';
        document.getElementById('loginPassword').value = '';
        document.getElementById('loginError').style.display = 'none';
    }
}

// التحقق من حالة تسجيل الدخول عند تحميل الصفحة
function checkLoginStatus() {
    const savedUser = localStorage.getItem('currentUser');

    if (savedUser) {
        try {
            currentUser = JSON.parse(savedUser);

            // إخفاء صفحة تسجيل الدخول وإظهار التطبيق
            document.getElementById('loginPage').style.display = 'none';
            document.getElementById('mainApp').style.display = 'block';

            // تحديث الواجهة حسب الصلاحيات
            updateUIBasedOnPermissions();

            return true;
        } catch (error) {
            console.error('خطأ في قراءة بيانات المستخدم:', error);
            localStorage.removeItem('currentUser');
        }
    }

    return false;
}

// عرض القسم المحدد
function showSection(sectionName) {
    // فحص الصلاحية للأقسام التي تحتاج صلاحيات
    if (['users', 'agents', 'collections'].includes(sectionName)) {
        if (!canView(sectionName)) {
            alert(`ليس لديك صلاحية مشاهدة ${getModuleName(sectionName)}`);
            return;
        }
    }

    // إخفاء جميع الأقسام
    document.querySelectorAll('.section').forEach(section => {
        section.style.display = 'none';
    });

    // عرض القسم المحدد
    document.getElementById(sectionName + '-section').style.display = 'block';

    // تحديث الأزرار
    document.querySelectorAll('.navbar-nav .btn').forEach(btn => {
        btn.classList.remove('btn-light');
        btn.classList.add('btn-outline-light');
    });

    // التحقق من وجود event قبل استخدامه
    if (typeof event !== 'undefined' && event.target) {
        event.target.classList.remove('btn-outline-light');
        event.target.classList.add('btn-light');
    }

    // تحميل البيانات حسب القسم
    switch(sectionName) {
        case 'dashboard':
            loadDashboard();
            break;
        case 'agents':
            stopDashboardAutoRefresh(); // إيقاف التحديث التلقائي
            loadAgents();
            break;
        case 'users':
            stopDashboardAutoRefresh(); // إيقاف التحديث التلقائي
            loadUsers();
            break;
        case 'collections':
            stopDashboardAutoRefresh(); // إيقاف التحديث التلقائي
            setTodayDate();
            break;
        case 'reports':
            stopDashboardAutoRefresh(); // إيقاف التحديث التلقائي
            setTodayDate('dailyReportDate');
            setDateRange();
            break;
    }
}

// تحميل الوكلاء
async function loadAgents() {
    try {
        const response = await fetch('/api/agents');
        const agents = await response.json();

        const tbody = document.getElementById('agentsTable');
        tbody.innerHTML = '';

        agents.forEach(agent => {
            const editButton = canEdit('agents') ?
                `<button class="btn btn-warning btn-sm me-1" onclick="editAgent(${agent.agent_id}, '${agent.agent_name}')">
                    <i class="fas fa-edit"></i> تعديل
                </button>` : '';

            const deleteButton = canDelete('agents') ?
                `<button class="btn btn-danger btn-sm" onclick="deleteAgent(${agent.agent_id})">
                    <i class="fas fa-trash"></i> حذف
                </button>` : '';

            const row = `
                <tr>
                    <td>${agent.agent_id}</td>
                    <td>${agent.agent_name}</td>
                    <td>
                        ${editButton}
                        ${deleteButton}
                    </td>
                </tr>
            `;
            tbody.innerHTML += row;
        });
    } catch (error) {
        console.error('خطأ في تحميل الوكلاء:', error);
        alert('خطأ في تحميل الوكلاء');
    }
}

// إضافة وكيل
async function addAgent() {
    if (!canAdd('agents')) {
        alert('ليس لديك صلاحية إضافة الوكلاء');
        return;
    }

    const agentName = document.getElementById('agentName').value.trim();

    if (!agentName) {
        alert('يرجى إدخال اسم الوكيل');
        return;
    }

    try {
        const response = await fetch('/api/agents', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ agent_name: agentName })
        });

        if (response.ok) {
            document.getElementById('agentName').value = '';
            loadAgents();
            showAlert('تم إضافة الوكيل بنجاح', 'success');
        } else {
            throw new Error('فشل في إضافة الوكيل');
        }
    } catch (error) {
        console.error('خطأ في إضافة الوكيل:', error);
        alert('خطأ في إضافة الوكيل');
    }
}

// تعديل وكيل
function editAgent(id, name) {
    if (!canEdit('agents')) {
        alert('ليس لديك صلاحية تعديل الوكلاء');
        return;
    }

    currentEditId = id;
    currentEditType = 'agent';

    document.getElementById('editModalTitle').textContent = 'تعديل الوكيل';
    document.getElementById('editModalBody').innerHTML = `
        <div class="mb-3">
            <label class="form-label">اسم الوكيل:</label>
            <input type="text" id="editAgentName" class="form-control" value="${name}">
        </div>
    `;

    document.getElementById('saveEditBtn').onclick = saveAgentEdit;
    new bootstrap.Modal(document.getElementById('editModal')).show();
}

// حفظ تعديل الوكيل
async function saveAgentEdit() {
    const agentName = document.getElementById('editAgentName').value.trim();

    if (!agentName) {
        alert('يرجى إدخال اسم الوكيل');
        return;
    }

    try {
        const response = await fetch(`/api/agents/${currentEditId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ agent_name: agentName })
        });

        if (response.ok) {
            bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
            loadAgents();
            showAlert('تم تحديث الوكيل بنجاح', 'success');
        } else {
            throw new Error('فشل في تحديث الوكيل');
        }
    } catch (error) {
        console.error('خطأ في تحديث الوكيل:', error);
        alert('خطأ في تحديث الوكيل');
    }
}

// حذف وكيل
async function deleteAgent(id) {
    if (!canDelete('agents')) {
        alert('ليس لديك صلاحية حذف الوكلاء');
        return;
    }

    if (!confirm('هل أنت متأكد من حذف هذا الوكيل؟')) {
        return;
    }

    try {
        const response = await fetch(`/api/agents/${id}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            loadAgents();
            showAlert('تم حذف الوكيل بنجاح', 'success');
        } else {
            throw new Error('فشل في حذف الوكيل');
        }
    } catch (error) {
        console.error('خطأ في حذف الوكيل:', error);
        alert('خطأ في حذف الوكيل');
    }
}

// تحميل المستخدمين
async function loadUsers() {
    try {
        const response = await fetch('/api/users');
        const users = await response.json();

        const tbody = document.getElementById('usersTable');
        tbody.innerHTML = '';

        users.forEach(user => {
            const editButton = canEdit('users') ?
                `<button class="btn btn-warning btn-sm me-1" onclick="editUser(${user.user_id}, '${user.employee_name}', '${user.username}')">
                    <i class="fas fa-edit"></i> تعديل
                </button>` : '';

            const deleteButton = canDelete('users') ?
                `<button class="btn btn-danger btn-sm" onclick="deleteUser(${user.user_id})">
                    <i class="fas fa-trash"></i> حذف
                </button>` : '';

            const row = `
                <tr>
                    <td>${user.user_id}</td>
                    <td>${user.employee_name}</td>
                    <td>${user.username}</td>
                    <td>
                        ${editButton}
                        ${deleteButton}
                    </td>
                </tr>
            `;
            tbody.innerHTML += row;
        });
    } catch (error) {
        console.error('خطأ في تحميل المستخدمين:', error);
        alert('خطأ في تحميل المستخدمين');
    }
}

// إضافة مستخدم
async function addUser() {
    if (!canAdd('users')) {
        alert('ليس لديك صلاحية إضافة المستخدمين');
        return;
    }

    const employeeName = document.getElementById('employeeName').value.trim();
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value.trim();

    if (!employeeName || !username || !password) {
        alert('يرجى ملء جميع الحقول');
        return;
    }

    try {
        const response = await fetch('/api/users', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ employee_name: employeeName, username, password })
        });

        if (response.ok) {
            document.getElementById('employeeName').value = '';
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';
            loadUsers();
            showAlert('تم إضافة المستخدم بنجاح', 'success');
        } else {
            throw new Error('فشل في إضافة المستخدم');
        }
    } catch (error) {
        console.error('خطأ في إضافة المستخدم:', error);
        alert('خطأ في إضافة المستخدم');
    }
}

// تعديل مستخدم
function editUser(id, employeeName, username) {
    currentEditId = id;
    currentEditType = 'user';

    document.getElementById('editModalTitle').textContent = 'تعديل المستخدم';
    document.getElementById('editModalBody').innerHTML = `
        <div class="mb-3">
            <label class="form-label">اسم الموظف:</label>
            <input type="text" id="editEmployeeName" class="form-control" value="${employeeName}">
        </div>
        <div class="mb-3">
            <label class="form-label">اسم الدخول:</label>
            <input type="text" id="editUsername" class="form-control" value="${username}">
        </div>
        <div class="mb-3">
            <label class="form-label">كلمة المرور الجديدة:</label>
            <input type="password" id="editPassword" class="form-control" placeholder="اتركها فارغة للاحتفاظ بكلمة المرور الحالية">
        </div>
    `;

    document.getElementById('saveEditBtn').onclick = saveUserEdit;
    new bootstrap.Modal(document.getElementById('editModal')).show();
}

// حفظ تعديل المستخدم
async function saveUserEdit() {
    const employeeName = document.getElementById('editEmployeeName').value.trim();
    const username = document.getElementById('editUsername').value.trim();
    const password = document.getElementById('editPassword').value.trim();

    if (!employeeName || !username) {
        alert('يرجى ملء الحقول المطلوبة');
        return;
    }

    const updateData = { employee_name: employeeName, username };
    if (password) {
        updateData.password = password;
    }

    try {
        const response = await fetch(`/api/users/${currentEditId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(updateData)
        });

        if (response.ok) {
            bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
            loadUsers();
            showAlert('تم تحديث المستخدم بنجاح', 'success');
        } else {
            throw new Error('فشل في تحديث المستخدم');
        }
    } catch (error) {
        console.error('خطأ في تحديث المستخدم:', error);
        alert('خطأ في تحديث المستخدم');
    }
}

// حذف مستخدم
async function deleteUser(id) {
    if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
        return;
    }

    try {
        const response = await fetch(`/api/users/${id}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            loadUsers();
            showAlert('تم حذف المستخدم بنجاح', 'success');
        } else {
            throw new Error('فشل في حذف المستخدم');
        }
    } catch (error) {
        console.error('خطأ في حذف المستخدم:', error);
        alert('خطأ في حذف المستخدم');
    }
}

// تحميل التحصيلات
async function loadCollections() {
    const date = document.getElementById('collectionDate').value;

    if (!date) {
        updateButtonsVisibility(false, false);
        return;
    }

    try {
        const response = await fetch(`/api/collections/${date}`);
        const collections = await response.json();

        // التحقق من وجود بيانات
        hasData = collections.some(c => c.gateway_amount > 0 || c.ryal_amount > 0);

        const tbody = document.getElementById('collectionsTable');
        tbody.innerHTML = '';

        collections.forEach(collection => {
            const row = `
                <tr data-agent-id="${collection.agent_id}">
                    <td class="select-column" style="display: none;">
                        <input type="checkbox" class="row-checkbox" data-agent-id="${collection.agent_id}" onchange="updateRowSelection(this)">
                    </td>
                    <td class="agent-name">${collection.agent_name}</td>
                    <td>
                        <input type="number" class="form-control collections-input"
                               value="${collection.gateway_amount}"
                               data-agent-id="${collection.agent_id}"
                               data-type="gateway"
                               onchange="updateTotal(this)"
                               ${hasData && !isEditMode ? 'disabled' : ''}>
                    </td>
                    <td>
                        <input type="number" class="form-control collections-input"
                               value="${collection.ryal_amount}"
                               data-agent-id="${collection.agent_id}"
                               data-type="ryal"
                               onchange="updateTotal(this)"
                               ${hasData && !isEditMode ? 'disabled' : ''}>
                    </td>
                    <td class="total-cell" id="total-${collection.agent_id}">
                        ${formatNumberEnglish(collection.total_amount)}
                    </td>
                </tr>
            `;
            tbody.innerHTML += row;
        });

        updateButtonsVisibility(true, hasData);
        isEditMode = false;
        isDeleteMode = false;
        hideSelectColumns();

    } catch (error) {
        console.error('خطأ في تحميل التحصيلات:', error);
        alert('خطأ في تحميل التحصيلات');
        updateButtonsVisibility(false, false);
    }
}

// تحديث الإجمالي
function updateTotal(input) {
    const agentId = input.dataset.agentId;
    const gatewayInput = document.querySelector(`input[data-agent-id="${agentId}"][data-type="gateway"]`);
    const ryalInput = document.querySelector(`input[data-agent-id="${agentId}"][data-type="ryal"]`);

    const gatewayAmount = parseFloat(gatewayInput.value) || 0;
    const ryalAmount = parseFloat(ryalInput.value) || 0;
    const total = gatewayAmount + ryalAmount;

    document.getElementById(`total-${agentId}`).textContent = formatNumberEnglish(total);
}

// تحديث رؤية الأزرار
function updateButtonsVisibility(dateSelected, hasExistingData) {
    const addBtn = document.getElementById('addCollectionBtn');
    const editBtn = document.getElementById('editCollectionBtn');
    const deleteBtn = document.getElementById('deleteCollectionBtn');
    const importBtn = document.getElementById('importCollectionBtn');
    const saveBtn = document.getElementById('saveBtn');
    const cancelBtn = document.getElementById('cancelBtn');
    const deleteSelectedBtn = document.getElementById('deleteSelectedBtn');

    // الأزرار الرئيسية مع فحص الصلاحيات
    if (dateSelected) {
        addBtn.style.display = canAdd('collections') ? 'inline-block' : 'none';
        editBtn.style.display = (hasExistingData && canEdit('collections')) ? 'inline-block' : 'none';
        deleteBtn.style.display = (hasExistingData && canDelete('collections')) ? 'inline-block' : 'none';
        importBtn.style.display = 'none'; // إخفاء زر الاستيراد في البداية
    } else {
        addBtn.style.display = 'none';
        editBtn.style.display = 'none';
        deleteBtn.style.display = 'none';
        importBtn.style.display = 'none';
    }

    // أزرار الحفظ والإلغاء
    if (isEditMode) {
        saveBtn.style.display = 'inline-block';
        cancelBtn.style.display = 'inline-block';
        addBtn.style.display = 'none';
        editBtn.style.display = 'none';
        deleteBtn.style.display = 'none';
        // إظهار زر الاستيراد فقط في وضع الإضافة (ليس التعديل)
        importBtn.style.display = (!hasExistingData && isEditMode) ? 'inline-block' : 'none';
    } else {
        saveBtn.style.display = 'none';
        cancelBtn.style.display = 'none';
    }

    // زر حذف المحدد
    deleteSelectedBtn.style.display = isDeleteMode ? 'inline-block' : 'none';
}

// إضافة تحصيل جديد
async function addNewCollection() {
    if (!canAdd('collections')) {
        alert('ليس لديك صلاحية إضافة التحصيلات');
        return;
    }

    const date = document.getElementById('collectionDate').value;

    if (!date) {
        alert('يرجى تحديد التاريخ أولاً');
        return;
    }

    try {
        // إضافة التاريخ إلى جدول الأيام إذا لم يكن موجوداً
        await fetch('/api/days', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ date })
        });

        // تفعيل وضع الإدخال
        const inputs = document.querySelectorAll('.collections-input');
        inputs.forEach(input => {
            input.disabled = false;
            input.value = 0;
        });

        // تحديث الإجماليات
        inputs.forEach(input => {
            if (input.dataset.type === 'gateway') {
                updateTotal(input);
            }
        });

        isEditMode = true;
        hasData = false;
        isDeleteMode = false;
        hideSelectColumns();
        updateButtonsVisibility(true, false);

    } catch (error) {
        console.error('خطأ في إضافة التحصيل الجديد:', error);
        alert('خطأ في إضافة التحصيل الجديد');
    }
}

// تفعيل وضع التحرير
function enableEdit() {
    if (!canEdit('collections')) {
        alert('ليس لديك صلاحية تعديل التحصيلات');
        return;
    }

    const date = document.getElementById('collectionDate').value;

    if (!date) {
        alert('يرجى تحديد التاريخ أولاً');
        return;
    }

    if (!hasData) {
        alert('لا توجد بيانات للتعديل');
        return;
    }

    const inputs = document.querySelectorAll('.collections-input');
    inputs.forEach(input => {
        input.disabled = false;
    });

    isEditMode = true;
    isDeleteMode = false;
    hideSelectColumns();
    updateButtonsVisibility(true, true);
}

// إلغاء التحرير
function cancelEdit() {
    isEditMode = false;
    isDeleteMode = false;
    hideSelectColumns();
    loadCollections(); // إعادة تحميل البيانات الأصلية
}

// تفعيل وضع الحذف
function enableDelete() {
    if (!canDelete('collections')) {
        alert('ليس لديك صلاحية حذف التحصيلات');
        return;
    }

    const date = document.getElementById('collectionDate').value;

    if (!date) {
        alert('يرجى تحديد التاريخ أولاً');
        return;
    }

    if (!hasData) {
        alert('لا توجد بيانات للحذف');
        return;
    }

    isDeleteMode = true;
    showSelectColumns();
    updateButtonsVisibility(true, hasData);
}

// إظهار أعمدة التحديد
function showSelectColumns() {
    document.getElementById('selectAllHeader').style.display = 'table-cell';
    document.querySelectorAll('.select-column').forEach(cell => {
        cell.style.display = 'table-cell';
    });
}

// إخفاء أعمدة التحديد
function hideSelectColumns() {
    document.getElementById('selectAllHeader').style.display = 'none';
    document.querySelectorAll('.select-column').forEach(cell => {
        cell.style.display = 'none';
    });

    // إلغاء تحديد جميع المربعات
    document.getElementById('selectAll').checked = false;
    document.querySelectorAll('.row-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });

    // إزالة تنسيق الصفوف المحددة
    document.querySelectorAll('.selected-row').forEach(row => {
        row.classList.remove('selected-row');
    });
}

// تحديد/إلغاء تحديد الكل
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.row-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
        updateRowSelection(checkbox);
    });
}

// تحديث تحديد الصف
function updateRowSelection(checkbox) {
    const row = checkbox.closest('tr');

    if (checkbox.checked) {
        row.classList.add('selected-row');
    } else {
        row.classList.remove('selected-row');
        document.getElementById('selectAll').checked = false;
    }

    // التحقق من تحديد جميع الصفوف
    const allCheckboxes = document.querySelectorAll('.row-checkbox');
    const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');

    if (allCheckboxes.length === checkedBoxes.length && allCheckboxes.length > 0) {
        document.getElementById('selectAll').checked = true;
    }
}

// حذف المحدد
async function deleteSelected() {
    const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');

    if (checkedBoxes.length === 0) {
        alert('يرجى تحديد عنصر واحد على الأقل للحذف');
        return;
    }

    const agentNames = [];
    checkedBoxes.forEach(checkbox => {
        const row = checkbox.closest('tr');
        const agentName = row.querySelector('.agent-name').textContent;
        agentNames.push(agentName);
    });

    const confirmMessage = `هل أنت متأكد من حذف التحصيلات للوكلاء التاليين؟\n${agentNames.join('\n')}`;

    if (!confirm(confirmMessage)) {
        return;
    }

    const date = document.getElementById('collectionDate').value;
    const agentIds = Array.from(checkedBoxes).map(checkbox => checkbox.dataset.agentId);

    try {
        const response = await fetch('/api/collections/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ date, agentIds })
        });

        if (response.ok) {
            showAlert('تم حذف التحصيلات المحددة بنجاح', 'success');
            isDeleteMode = false;
            hideSelectColumns();
            loadCollections();
        } else {
            throw new Error('فشل في حذف التحصيلات');
        }
    } catch (error) {
        console.error('خطأ في حذف التحصيلات:', error);
        alert('خطأ في حذف التحصيلات');
    }
}

// حفظ التحصيلات
async function saveCollections() {
    const date = document.getElementById('collectionDate').value;

    if (!date) {
        alert('يرجى تحديد التاريخ');
        return;
    }

    const collections = [];
    const inputs = document.querySelectorAll('.collections-input');

    // تجميع البيانات حسب الوكيل
    const agentData = {};
    inputs.forEach(input => {
        const agentId = input.dataset.agentId;
        const type = input.dataset.type;
        const amount = parseFloat(input.value) || 0;

        if (!agentData[agentId]) {
            agentData[agentId] = { agent_id: agentId, gateway_amount: 0, ryal_amount: 0 };
        }

        if (type === 'gateway') {
            agentData[agentId].gateway_amount = amount;
        } else if (type === 'ryal') {
            agentData[agentId].ryal_amount = amount;
        }
    });

    Object.values(agentData).forEach(data => {
        collections.push(data);
    });

    try {
        const response = await fetch('/api/collections', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ date, collections })
        });

        if (response.ok) {
            showAlert('تم حفظ التحصيلات بنجاح', 'success');

            // تعطيل الحقول وتحديث الحالة
            inputs.forEach(input => {
                input.disabled = true;
            });

            hasData = true;
            isEditMode = false;
            updateButtonsVisibility(true, true);

        } else {
            throw new Error('فشل في حفظ التحصيلات');
        }
    } catch (error) {
        console.error('خطأ في حفظ التحصيلات:', error);
        alert('خطأ في حفظ التحصيلات');
    }
}

// تقرير يومي
async function generateDailyReport() {
    const date = document.getElementById('dailyReportDate').value;

    if (!date) {
        alert('يرجى تحديد التاريخ');
        return;
    }

    try {
        const response = await fetch(`/api/reports/daily/${date}`);
        const data = await response.json();

        let html = `
            <div class="table-responsive">
                <table class="table table-bordered report-table">
                    <thead>
                        <tr>
                            <th>اسم الوكيل</th>
                            <th>البوابة</th>
                            <th>ريال موبايل</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        let totalGateway = 0;
        let totalRyal = 0;
        let grandTotal = 0;

        data.forEach(row => {
            totalGateway += parseFloat(row.gateway_amount);
            totalRyal += parseFloat(row.ryal_amount);
            grandTotal += parseFloat(row.total_amount);

            html += `
                <tr>
                    <td class="report-agent-name">${row.agent_name}</td>
                    <td class="number-cell">${formatNumberEnglish(row.gateway_amount)}</td>
                    <td class="number-cell">${formatNumberEnglish(row.ryal_amount)}</td>
                    <td class="number-cell large-number">${formatNumberEnglish(row.total_amount)}</td>
                </tr>
            `;
        });

        html += `
                    <tr class="total-row">
                        <td><strong>الإجمالي</strong></td>
                        <td class="number-cell"><strong>${formatNumberEnglish(totalGateway)}</strong></td>
                        <td class="number-cell"><strong>${formatNumberEnglish(totalRyal)}</strong></td>
                        <td class="number-cell"><strong>${formatNumberEnglish(grandTotal)}</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>
        `;

        document.getElementById('reportTitle').textContent = `التقرير اليومي - ${date}`;
        document.getElementById('reportContent').innerHTML = html;
        document.getElementById('reportResults').style.display = 'block';

    } catch (error) {
        console.error('خطأ في إنشاء التقرير:', error);
        alert('خطأ في إنشاء التقرير');
    }
}

// تقرير حسب الفترة
async function generatePeriodReport() {
    const fromDate = document.getElementById('fromDate').value;
    const toDate = document.getElementById('toDate').value;
    const summary = document.getElementById('summaryReport').checked;

    if (!fromDate || !toDate) {
        alert('يرجى تحديد الفترة');
        return;
    }

    try {
        // إضافة timestamp لتجنب cache المتصفح
        const timestamp = new Date().getTime();
        const response = await fetch(`/api/reports/period?from_date=${fromDate}&to_date=${toDate}&summary=${summary}&_t=${timestamp}`);
        const data = await response.json();

        // تتبع البيانات للتشخيص (يمكن إزالتها لاحقاً)
        // console.log('البيانات المستلمة من الخادم:', data.slice(0, 2));

        let html = '';

        if (summary) {
            // تقرير إجمالي - يعرض إجمالي كل وكيل للفترة المحددة
            // نفس الإجماليات التي تظهر في نهاية التقرير التفصيلي
            html = `
                <div class="table-responsive">
                    <table class="table table-bordered report-table">
                        <thead>
                            <tr>
                                <th>اسم الوكيل</th>
                                <th>إجمالي البوابة</th>
                                <th>إجمالي ريال موبايل</th>
                                <th>الإجمالي العام</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            let totalGateway = 0;
            let totalRyal = 0;
            let grandTotal = 0;

            data.forEach(row => {
                totalGateway += parseFloat(row.total_gateway);
                totalRyal += parseFloat(row.total_ryal);
                grandTotal += parseFloat(row.grand_total);

                html += `
                    <tr>
                        <td class="report-agent-name">${row.agent_name}</td>
                        <td class="number-cell">${formatNumberEnglish(row.total_gateway)}</td>
                        <td class="number-cell">${formatNumberEnglish(row.total_ryal)}</td>
                        <td class="number-cell large-number">${formatNumberEnglish(row.grand_total)}</td>
                    </tr>
                `;
            });

            // إجمالي عام للفترة - نفس الإجمالي الذي يظهر في التقرير التفصيلي
            html += `
                        <tr class="total-row">
                            <td><strong>الإجمالي العام للفترة</strong></td>
                            <td class="number-cell"><strong>${formatNumberEnglish(totalGateway)}</strong></td>
                            <td class="number-cell"><strong>${formatNumberEnglish(totalRyal)}</strong></td>
                            <td class="number-cell"><strong>${formatNumberEnglish(grandTotal)}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            `;

            document.getElementById('reportTitle').textContent = `التقرير الإجمالي من ${fromDate} إلى ${toDate}`;
        } else {
            // تقرير مفصل
            // تجميع البيانات حسب التاريخ والوكيل
            const groupedData = {};
            const agents = new Set();

            data.forEach(row => {
                // التأكد من تنسيق التاريخ بشكل صحيح
                let formattedDate = row.date_id;

                // إذا كان التاريخ ما زال بالصيغة الطويلة، قم بتنسيقه
                if (formattedDate && formattedDate.includes('T')) {
                    formattedDate = formatDateOnly(formattedDate);
                } else if (formattedDate && formattedDate.includes('GMT')) {
                    formattedDate = formatDateOnly(formattedDate);
                }

                if (!groupedData[formattedDate]) {
                    groupedData[formattedDate] = {};
                }
                groupedData[formattedDate][row.agent_name] = {
                    gateway: row.gateway_amount,
                    ryal: row.ryal_amount
                };
                agents.add(row.agent_name);
            });

            const agentsList = Array.from(agents).sort();

            html = `
                <div class="table-responsive detailed-report">
                    <table class="table table-bordered report-table">
                        <thead class="sticky-header">
                            <tr>
                                <th rowspan="2">التاريخ</th>
            `;

            agentsList.forEach((agent, index) => {
                const colorClass = `agent-color-${(index % 11) + 1}`;
                html += `<th colspan="2" class="${colorClass}">${agent}</th>`;
            });

            html += `
                            </tr>
                            <tr>
            `;

            agentsList.forEach((agent, index) => {
                const colorClass = `agent-color-${(index % 11) + 1}`;
                html += `<th class="${colorClass}">البوابة</th><th class="${colorClass}">ريال موبايل</th>`;
            });

            html += `
                            </tr>
                        </thead>
                        <tbody>
            `;

            const totals = {};
            agentsList.forEach(agent => {
                totals[agent] = { gateway: 0, ryal: 0 };
            });

            // ترتيب التواريخ بشكل صحيح
            Object.keys(groupedData).sort((a, b) => new Date(a) - new Date(b)).forEach(date => {
                html += `<tr><td><strong>${date}</strong></td>`;

                agentsList.forEach((agent, index) => {
                    const agentData = groupedData[date][agent] || { gateway: 0, ryal: 0 };
                    totals[agent].gateway += parseFloat(agentData.gateway);
                    totals[agent].ryal += parseFloat(agentData.ryal);

                    const colorClass = `agent-color-${(index % 11) + 1}`;
                    html += `
                        <td class="number-cell ${colorClass}">${formatNumberEnglish(agentData.gateway)}</td>
                        <td class="number-cell ${colorClass}">${formatNumberEnglish(agentData.ryal)}</td>
                    `;
                });

                html += `</tr>`;
            });

            // سطر الإجمالي
            html += `<tr class="total-row"><td><strong>الإجمالي</strong></td>`;
            agentsList.forEach((agent, index) => {
                const colorClass = `agent-color-${(index % 11) + 1}`;
                html += `
                    <td class="number-cell ${colorClass}"><strong>${formatNumberEnglish(totals[agent].gateway)}</strong></td>
                    <td class="number-cell ${colorClass}"><strong>${formatNumberEnglish(totals[agent].ryal)}</strong></td>
                `;
            });
            html += `</tr>`;

            html += `
                    </tbody>
                </table>
            </div>
            `;

            document.getElementById('reportTitle').textContent = `التقرير المفصل من ${fromDate} إلى ${toDate}`;
        }

        document.getElementById('reportContent').innerHTML = html;
        document.getElementById('reportResults').style.display = 'block';

    } catch (error) {
        console.error('خطأ في إنشاء التقرير:', error);
        alert('خطأ في إنشاء التقرير');
    }
}

// دوال مساعدة
function formatNumber(num) {
    return parseFloat(num).toLocaleString('ar-SA', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    });
}

function formatNumberEnglish(num) {
    return parseFloat(num).toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    });
}

// دالة تنسيق التاريخ (عرض التاريخ فقط بدون الوقت)
function formatDateOnly(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
}

function setTodayDate(elementId = 'collectionDate') {
    const today = new Date();
    const formattedDate = formatDateOnly(today);
    document.getElementById(elementId).value = formattedDate;
}

function setDateRange() {
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);

    document.getElementById('fromDate').value = formatDateOnly(firstDay);
    document.getElementById('toDate').value = formatDateOnly(today);
}

function showAlert(message, type) {
    // يمكن تحسين هذه الدالة لاحقاً لعرض تنبيهات أفضل
    alert(message);
}

// تحديث نص معلومات التقرير
function updateReportTypeInfo() {
    const summaryCheckbox = document.getElementById('summaryReport');
    const infoText = document.getElementById('reportTypeInfo');

    if (summaryCheckbox.checked) {
        infoText.textContent = 'تقرير إجمالي: عرض إجمالي الفترة لكل وكيل';
    } else {
        infoText.textContent = 'تقرير تفصيلي: عرض البيانات يوم بيوم';
    }
}

// === وظائف لوحة التحكم ===

// متغيرات الرسوم البيانية
let topAgentsChart = null;
let weeklyTrendChart = null;
let dashboardInterval = null;

// تحميل لوحة التحكم
async function loadDashboard() {
    try {
        console.log('بدء تحميل لوحة التحكم...');

        // التحقق من تحميل Chart.js
        if (typeof Chart === 'undefined') {
            console.error('Chart.js غير محمل');
            // محاولة تحميل البيانات الأساسية على الأقل
            await loadDashboardStats();
            await loadAgentsPerformance();
            return;
        }

        // تحميل البيانات الأساسية أولاً
        await loadDashboardStats();
        await loadAgentsPerformance();

        // تحميل الرسوم البيانية مع تأخير أطول
        setTimeout(async () => {
            try {
                await loadTopAgentsChart();
                await loadWeeklyTrendChart();
                console.log('تم تحميل لوحة التحكم بنجاح');

                // بدء التحديث التلقائي
                startDashboardAutoRefresh();
            } catch (chartError) {
                console.error('خطأ في تحميل الرسوم البيانية:', chartError);
            }
        }, 800);

    } catch (error) {
        console.error('خطأ في تحميل لوحة التحكم:', error);
    }
}

// بدء التحديث التلقائي للوحة التحكم
function startDashboardAutoRefresh() {
    // إيقاف التحديث السابق إن وجد
    if (dashboardInterval) {
        clearInterval(dashboardInterval);
    }

    // تحديث البيانات كل 5 دقائق
    dashboardInterval = setInterval(async () => {
        try {
            await loadDashboardStats();
            await loadAgentsPerformance();
            console.log('تم تحديث بيانات لوحة التحكم تلقائياً');
        } catch (error) {
            console.error('خطأ في التحديث التلقائي:', error);
        }
    }, 300000); // 5 دقائق
}

// إيقاف التحديث التلقائي
function stopDashboardAutoRefresh() {
    if (dashboardInterval) {
        clearInterval(dashboardInterval);
        dashboardInterval = null;
    }
}

// تحميل الإحصائيات السريعة
async function loadDashboardStats() {
    try {
        const response = await fetch('/api/dashboard/stats');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();

        document.getElementById('totalAgents').textContent = data.totalAgents || '0';
        document.getElementById('todayTotal').textContent = formatNumberEnglish(data.todayTotal || 0);
        document.getElementById('weekTotal').textContent = formatNumberEnglish(data.weekTotal || 0);
        document.getElementById('monthTotal').textContent = formatNumberEnglish(data.monthTotal || 0);
    } catch (error) {
        console.error('خطأ في تحميل الإحصائيات:', error);
        // عرض قيم افتراضية في حالة الخطأ
        document.getElementById('totalAgents').textContent = '0';
        document.getElementById('todayTotal').textContent = '0';
        document.getElementById('weekTotal').textContent = '0';
        document.getElementById('monthTotal').textContent = '0';
    }
}

// تحميل أداء الوكلاء
async function loadAgentsPerformance() {
    try {
        const response = await fetch('/api/dashboard/agents-performance');
        const data = await response.json();

        const tbody = document.getElementById('agentsPerformanceTable');
        tbody.innerHTML = '';

        data.forEach(agent => {
            const todayTotal = parseFloat(agent.today_total);
            const weekTotal = parseFloat(agent.week_total);
            const monthTotal = parseFloat(agent.month_total);
            const dailyAverage = monthTotal / 30;

            // تحديد حالة الأداء
            let status = 'ضعيف';
            let statusClass = 'status-poor';

            if (weekTotal > 500000000) {
                status = 'ممتاز';
                statusClass = 'status-excellent';
            } else if (weekTotal > 300000000) {
                status = 'جيد';
                statusClass = 'status-good';
            } else if (weekTotal > 100000000) {
                status = 'متوسط';
                statusClass = 'status-average';
            }

            const row = `
                <tr>
                    <td class="report-agent-name">${agent.agent_name}</td>
                    <td class="number-currency">${formatNumberEnglish(todayTotal)}</td>
                    <td class="number-currency">${formatNumberEnglish(weekTotal)}</td>
                    <td class="number-currency">${formatNumberEnglish(monthTotal)}</td>
                    <td class="number-currency">${formatNumberEnglish(dailyAverage)}</td>
                    <td><span class="status-badge ${statusClass}">${status}</span></td>
                </tr>
            `;
            tbody.innerHTML += row;
        });
    } catch (error) {
        console.error('خطأ في تحميل أداء الوكلاء:', error);
    }
}

// تحميل رسم أفضل الوكلاء
async function loadTopAgentsChart() {
    try {
        const response = await fetch('/api/dashboard/top-agents');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();

        const ctx = document.getElementById('topAgentsChart');

        // إزالة الرسم السابق إن وجد
        if (topAgentsChart) {
            topAgentsChart.destroy();
        }

        // إنشاء canvas جديد إذا لم يكن موجوداً
        if (!ctx.querySelector('canvas')) {
            ctx.innerHTML = '<canvas></canvas>';
        }

        const canvas = ctx.querySelector('canvas');

        // التحقق من وجود بيانات
        if (!data || data.length === 0) {
            ctx.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-chart-pie fa-2x mb-2"></i><br>لا توجد بيانات لعرضها</div>';
            return;
        }

        // فلترة البيانات التي لها قيم أكبر من صفر
        const filteredData = data.filter(agent => parseFloat(agent.week_total || 0) > 0);

        if (filteredData.length === 0) {
            ctx.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-chart-pie fa-2x mb-2"></i><br>لا توجد تحصيلات للعرض</div>';
            return;
        }

        try {
            topAgentsChart = new Chart(canvas, {
                type: 'doughnut',
                data: {
                    labels: filteredData.map(agent => agent.agent_name),
                    datasets: [{
                        data: filteredData.map(agent => parseFloat(agent.week_total || 0)),
                        backgroundColor: [
                            '#FF6384',
                            '#36A2EB',
                            '#FFCE56',
                            '#4BC0C0',
                            '#9966FF'
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const value = formatNumberEnglish(context.parsed);
                                    return `${context.label}: ${value}`;
                                }
                            }
                        }
                    }
                }
            });
            console.log('تم إنشاء رسم أفضل الوكلاء بنجاح');
        } catch (chartError) {
            console.error('خطأ في إنشاء الرسم:', chartError);
            ctx.innerHTML = '<div class="text-center text-danger p-4">خطأ في تحميل الرسم البياني</div>';
        }
    } catch (error) {
        console.error('خطأ في تحميل رسم أفضل الوكلاء:', error);
    }
}

// تحميل رسم الاتجاه الأسبوعي
async function loadWeeklyTrendChart() {
    try {
        const response = await fetch('/api/dashboard/weekly-trend');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();

        const ctx = document.getElementById('weeklyTrendChart');

        // إزالة الرسم السابق إن وجد
        if (weeklyTrendChart) {
            weeklyTrendChart.destroy();
        }

        // إنشاء canvas جديد إذا لم يكن موجوداً
        if (!ctx.querySelector('canvas')) {
            ctx.innerHTML = '<canvas></canvas>';
        }

        const canvas = ctx.querySelector('canvas');

        // التحقق من وجود بيانات
        if (!data || data.length === 0) {
            ctx.innerHTML = '<div class="text-center text-muted p-4"><i class="fas fa-chart-line fa-2x mb-2"></i><br>لا توجد بيانات لعرضها</div>';
            return;
        }

        try {
            weeklyTrendChart = new Chart(canvas, {
                type: 'line',
                data: {
                    labels: data.map(item => item.date_id),
                    datasets: [{
                        label: 'التحصيلات اليومية',
                        data: data.map(item => parseFloat(item.daily_total || 0)),
                        borderColor: '#28a745',
                        backgroundColor: 'rgba(40, 167, 69, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#28a745',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const value = formatNumberEnglish(context.parsed.y);
                                    return `التحصيلات: ${value}`;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return formatNumberEnglish(value);
                                }
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
            console.log('تم إنشاء رسم الاتجاه الأسبوعي بنجاح');
        } catch (chartError) {
            console.error('خطأ في إنشاء الرسم الخطي:', chartError);
            ctx.innerHTML = '<div class="text-center text-danger p-4">خطأ في تحميل الرسم البياني</div>';
        }
    } catch (error) {
        console.error('خطأ في تحميل رسم الاتجاه الأسبوعي:', error);
    }
}

// تحديث لوحة التحكم
async function refreshDashboard() {
    const button = document.querySelector('[onclick="refreshDashboard()"]');
    const originalText = button.innerHTML;

    button.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري التحديث...';
    button.disabled = true;

    try {
        await loadDashboard();
        showAlert('تم تحديث لوحة التحكم بنجاح', 'success');
    } catch (error) {
        showAlert('خطأ في تحديث لوحة التحكم', 'error');
    } finally {
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// متغير لحفظ قائمة الوكلاء
let agentsList = [];

// دوال الاستيراد
async function showImportModal() {
    // تحميل قائمة الوكلاء أولاً
    try {
        const response = await fetch('/api/agents');
        agentsList = await response.json();
    } catch (error) {
        console.error('خطأ في تحميل قائمة الوكلاء:', error);
        alert('خطأ في تحميل قائمة الوكلاء');
        return;
    }

    const modal = new bootstrap.Modal(document.getElementById('importModal'));
    modal.show();

    // مسح النص السابق
    document.getElementById('importText').value = '';
    document.getElementById('previewArea').style.display = 'none';
}

function previewImport() {
    const text = document.getElementById('importText').value.trim();
    if (!text) {
        alert('يرجى إدخال النص المراد استيراده');
        return;
    }

    const lines = text.split('\n').filter(line => line.trim());
    const previewTable = document.getElementById('previewTable');
    const previewArea = document.getElementById('previewArea');

    previewTable.innerHTML = '';

    lines.forEach((line, index) => {
        const parts = line.trim().split(/\s+/);
        const row = document.createElement('tr');

        if (parts.length >= 1) {
            const agentName = parts[0];
            const gatewayAmount = parts[1] ? parseFloat(parts[1]) || 0 : 0;
            const ryalAmount = parts[2] ? parseFloat(parts[2]) || 0 : 0;

            // التحقق من وجود الوكيل
            const agentExists = agentsList.some(agent => agent.agent_name === agentName);
            const status = agentExists ?
                '<span class="badge bg-success">موجود</span>' :
                '<span class="badge bg-warning">غير موجود</span>';

            row.innerHTML = `
                <td>${agentName}</td>
                <td>${formatNumber(gatewayAmount)}</td>
                <td>${formatNumber(ryalAmount)}</td>
                <td>${status}</td>
            `;

            if (!agentExists) {
                row.classList.add('table-warning');
            }
        } else {
            row.innerHTML = `
                <td colspan="4" class="text-danger">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    خطأ في السطر ${index + 1}: تنسيق غير صحيح
                </td>
            `;
            row.classList.add('table-danger');
        }

        previewTable.appendChild(row);
    });

    previewArea.style.display = 'block';
}

function importCollections() {
    const text = document.getElementById('importText').value.trim();
    if (!text) {
        alert('يرجى إدخال النص المراد استيراده');
        return;
    }

    // التحقق من وجود جدول التحصيلات
    const tableBody = document.getElementById('collectionsTable');
    if (!tableBody || tableBody.children.length === 0) {
        alert('يرجى تحديد تاريخ وتحميل جدول التحصيلات أولاً');
        return;
    }

    const lines = text.split('\n').filter(line => line.trim());
    let importedCount = 0;
    let skippedCount = 0;

    lines.forEach(line => {
        const parts = line.trim().split(/\s+/);

        if (parts.length >= 1) {
            const agentName = parts[0];
            const gatewayAmount = parts[1] ? parseFloat(parts[1]) || 0 : 0;
            const ryalAmount = parts[2] ? parseFloat(parts[2]) || 0 : 0;

            // البحث عن الوكيل
            const agent = agentsList.find(a => a.agent_name === agentName);

            if (agent) {
                // البحث عن السطر في الجدول
                const rows = tableBody.querySelectorAll('tr');
                let found = false;

                for (let row of rows) {
                    // البحث عن خلية اسم الوكيل (تجاهل عمود التحديد)
                    const cells = row.querySelectorAll('td');
                    let agentCell = null;

                    for (let cell of cells) {
                        if (!cell.classList.contains('select-column') &&
                            !cell.querySelector('input') &&
                            cell.textContent.trim() === agentName) {
                            agentCell = cell;
                            break;
                        }
                    }

                    if (agentCell) {
                        // تحديث المبالغ
                        const gatewayInput = row.querySelector('input[data-type="gateway"]');
                        const ryalInput = row.querySelector('input[data-type="ryal"]');

                        if (gatewayInput) {
                            gatewayInput.value = gatewayAmount;
                            updateTotal(gatewayInput);
                        }
                        if (ryalInput) {
                            ryalInput.value = ryalAmount;
                            updateTotal(ryalInput);
                        }

                        importedCount++;
                        found = true;
                        break;
                    }
                }

                if (!found) {
                    skippedCount++;
                }
            } else {
                skippedCount++;
            }
        }
    });

    // إغلاق النافذة
    const modal = bootstrap.Modal.getInstance(document.getElementById('importModal'));
    modal.hide();

    // عرض رسالة النتيجة
    const message = `تم استيراد ${importedCount} عنصر بنجاح`;
    const skippedMessage = skippedCount > 0 ? `\nتم تجاهل ${skippedCount} عنصر (وكلاء غير موجودين)` : '';

    alert(message + skippedMessage);

    // تفعيل وضع التعديل إذا لم يكن مفعلاً
    if (!isEditMode) {
        enableEdit();
    }
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('تم تحميل الصفحة، بدء التهيئة...');

    // إضافة مستمع لنموذج تسجيل الدخول
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }

    // التحقق من حالة تسجيل الدخول
    const isLoggedIn = checkLoginStatus();

    if (isLoggedIn) {
        // المستخدم مسجل دخول، تحميل التطبيق
        setTimeout(() => {
            // التأكد من تحميل Chart.js قبل عرض لوحة التحكم
            if (typeof Chart !== 'undefined') {
                console.log('Chart.js محمل بنجاح');
                showSection('dashboard'); // جعل لوحة التحكم هي الصفحة الرئيسية
            } else {
                console.log('Chart.js غير محمل، انتظار...');
                // إذا لم يتم تحميل Chart.js، انتظر أكثر ثم حاول مرة أخرى
                setTimeout(() => {
                    if (typeof Chart !== 'undefined') {
                        console.log('Chart.js محمل بعد الانتظار');
                        showSection('dashboard');
                    } else {
                        console.error('فشل في تحميل Chart.js');
                        // عرض لوحة التحكم بدون رسوم بيانية
                        showSection('dashboard');
                    }
                }, 2000);
            }
        }, 300);
    } else {
        // المستخدم غير مسجل دخول، إظهار صفحة تسجيل الدخول
        document.getElementById('loginPage').style.display = 'block';
        document.getElementById('mainApp').style.display = 'none';
    }

    // إضافة مستمع لتغيير مربع التحديد
    const summaryCheckbox = document.getElementById('summaryReport');
    if (summaryCheckbox) {
        summaryCheckbox.addEventListener('change', updateReportTypeInfo);
        updateReportTypeInfo(); // تحديث النص الأولي
    }
});
