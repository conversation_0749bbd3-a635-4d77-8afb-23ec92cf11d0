// متغيرات عامة
let currentEditId = null;
let currentEditType = null;
let isEditMode = false;
let hasData = false;
let isDeleteMode = false;

// عرض القسم المحدد
function showSection(sectionName) {
    // إخفاء جميع الأقسام
    document.querySelectorAll('.section').forEach(section => {
        section.style.display = 'none';
    });

    // عرض القسم المحدد
    document.getElementById(sectionName + '-section').style.display = 'block';

    // تحديث الأزرار
    document.querySelectorAll('.navbar-nav .btn').forEach(btn => {
        btn.classList.remove('btn-light');
        btn.classList.add('btn-outline-light');
    });
    event.target.classList.remove('btn-outline-light');
    event.target.classList.add('btn-light');

    // تحميل البيانات حسب القسم
    switch(sectionName) {
        case 'agents':
            loadAgents();
            break;
        case 'users':
            loadUsers();
            break;
        case 'collections':
            setTodayDate();
            break;
        case 'reports':
            setTodayDate('dailyReportDate');
            setDateRange();
            break;
    }
}

// تحميل الوكلاء
async function loadAgents() {
    try {
        const response = await fetch('/api/agents');
        const agents = await response.json();

        const tbody = document.getElementById('agentsTable');
        tbody.innerHTML = '';

        agents.forEach(agent => {
            const row = `
                <tr>
                    <td>${agent.agent_id}</td>
                    <td>${agent.agent_name}</td>
                    <td>
                        <button class="btn btn-warning btn-sm me-1" onclick="editAgent(${agent.agent_id}, '${agent.agent_name}')">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="deleteAgent(${agent.agent_id})">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </td>
                </tr>
            `;
            tbody.innerHTML += row;
        });
    } catch (error) {
        console.error('خطأ في تحميل الوكلاء:', error);
        alert('خطأ في تحميل الوكلاء');
    }
}

// إضافة وكيل
async function addAgent() {
    const agentName = document.getElementById('agentName').value.trim();

    if (!agentName) {
        alert('يرجى إدخال اسم الوكيل');
        return;
    }

    try {
        const response = await fetch('/api/agents', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ agent_name: agentName })
        });

        if (response.ok) {
            document.getElementById('agentName').value = '';
            loadAgents();
            showAlert('تم إضافة الوكيل بنجاح', 'success');
        } else {
            throw new Error('فشل في إضافة الوكيل');
        }
    } catch (error) {
        console.error('خطأ في إضافة الوكيل:', error);
        alert('خطأ في إضافة الوكيل');
    }
}

// تعديل وكيل
function editAgent(id, name) {
    currentEditId = id;
    currentEditType = 'agent';

    document.getElementById('editModalTitle').textContent = 'تعديل الوكيل';
    document.getElementById('editModalBody').innerHTML = `
        <div class="mb-3">
            <label class="form-label">اسم الوكيل:</label>
            <input type="text" id="editAgentName" class="form-control" value="${name}">
        </div>
    `;

    document.getElementById('saveEditBtn').onclick = saveAgentEdit;
    new bootstrap.Modal(document.getElementById('editModal')).show();
}

// حفظ تعديل الوكيل
async function saveAgentEdit() {
    const agentName = document.getElementById('editAgentName').value.trim();

    if (!agentName) {
        alert('يرجى إدخال اسم الوكيل');
        return;
    }

    try {
        const response = await fetch(`/api/agents/${currentEditId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ agent_name: agentName })
        });

        if (response.ok) {
            bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
            loadAgents();
            showAlert('تم تحديث الوكيل بنجاح', 'success');
        } else {
            throw new Error('فشل في تحديث الوكيل');
        }
    } catch (error) {
        console.error('خطأ في تحديث الوكيل:', error);
        alert('خطأ في تحديث الوكيل');
    }
}

// حذف وكيل
async function deleteAgent(id) {
    if (!confirm('هل أنت متأكد من حذف هذا الوكيل؟')) {
        return;
    }

    try {
        const response = await fetch(`/api/agents/${id}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            loadAgents();
            showAlert('تم حذف الوكيل بنجاح', 'success');
        } else {
            throw new Error('فشل في حذف الوكيل');
        }
    } catch (error) {
        console.error('خطأ في حذف الوكيل:', error);
        alert('خطأ في حذف الوكيل');
    }
}

// تحميل المستخدمين
async function loadUsers() {
    try {
        const response = await fetch('/api/users');
        const users = await response.json();

        const tbody = document.getElementById('usersTable');
        tbody.innerHTML = '';

        users.forEach(user => {
            const row = `
                <tr>
                    <td>${user.user_id}</td>
                    <td>${user.employee_name}</td>
                    <td>${user.username}</td>
                    <td>
                        <button class="btn btn-warning btn-sm me-1" onclick="editUser(${user.user_id}, '${user.employee_name}', '${user.username}')">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="deleteUser(${user.user_id})">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </td>
                </tr>
            `;
            tbody.innerHTML += row;
        });
    } catch (error) {
        console.error('خطأ في تحميل المستخدمين:', error);
        alert('خطأ في تحميل المستخدمين');
    }
}

// إضافة مستخدم
async function addUser() {
    const employeeName = document.getElementById('employeeName').value.trim();
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value.trim();

    if (!employeeName || !username || !password) {
        alert('يرجى ملء جميع الحقول');
        return;
    }

    try {
        const response = await fetch('/api/users', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ employee_name: employeeName, username, password })
        });

        if (response.ok) {
            document.getElementById('employeeName').value = '';
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';
            loadUsers();
            showAlert('تم إضافة المستخدم بنجاح', 'success');
        } else {
            throw new Error('فشل في إضافة المستخدم');
        }
    } catch (error) {
        console.error('خطأ في إضافة المستخدم:', error);
        alert('خطأ في إضافة المستخدم');
    }
}

// تعديل مستخدم
function editUser(id, employeeName, username) {
    currentEditId = id;
    currentEditType = 'user';

    document.getElementById('editModalTitle').textContent = 'تعديل المستخدم';
    document.getElementById('editModalBody').innerHTML = `
        <div class="mb-3">
            <label class="form-label">اسم الموظف:</label>
            <input type="text" id="editEmployeeName" class="form-control" value="${employeeName}">
        </div>
        <div class="mb-3">
            <label class="form-label">اسم الدخول:</label>
            <input type="text" id="editUsername" class="form-control" value="${username}">
        </div>
        <div class="mb-3">
            <label class="form-label">كلمة المرور الجديدة:</label>
            <input type="password" id="editPassword" class="form-control" placeholder="اتركها فارغة للاحتفاظ بكلمة المرور الحالية">
        </div>
    `;

    document.getElementById('saveEditBtn').onclick = saveUserEdit;
    new bootstrap.Modal(document.getElementById('editModal')).show();
}

// حفظ تعديل المستخدم
async function saveUserEdit() {
    const employeeName = document.getElementById('editEmployeeName').value.trim();
    const username = document.getElementById('editUsername').value.trim();
    const password = document.getElementById('editPassword').value.trim();

    if (!employeeName || !username) {
        alert('يرجى ملء الحقول المطلوبة');
        return;
    }

    const updateData = { employee_name: employeeName, username };
    if (password) {
        updateData.password = password;
    }

    try {
        const response = await fetch(`/api/users/${currentEditId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(updateData)
        });

        if (response.ok) {
            bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
            loadUsers();
            showAlert('تم تحديث المستخدم بنجاح', 'success');
        } else {
            throw new Error('فشل في تحديث المستخدم');
        }
    } catch (error) {
        console.error('خطأ في تحديث المستخدم:', error);
        alert('خطأ في تحديث المستخدم');
    }
}

// حذف مستخدم
async function deleteUser(id) {
    if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
        return;
    }

    try {
        const response = await fetch(`/api/users/${id}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            loadUsers();
            showAlert('تم حذف المستخدم بنجاح', 'success');
        } else {
            throw new Error('فشل في حذف المستخدم');
        }
    } catch (error) {
        console.error('خطأ في حذف المستخدم:', error);
        alert('خطأ في حذف المستخدم');
    }
}

// تحميل التحصيلات
async function loadCollections() {
    const date = document.getElementById('collectionDate').value;

    if (!date) {
        updateButtonsVisibility(false, false);
        return;
    }

    try {
        const response = await fetch(`/api/collections/${date}`);
        const collections = await response.json();

        // التحقق من وجود بيانات
        hasData = collections.some(c => c.gateway_amount > 0 || c.ryal_amount > 0);

        const tbody = document.getElementById('collectionsTable');
        tbody.innerHTML = '';

        collections.forEach(collection => {
            const row = `
                <tr data-agent-id="${collection.agent_id}">
                    <td class="select-column" style="display: none;">
                        <input type="checkbox" class="row-checkbox" data-agent-id="${collection.agent_id}" onchange="updateRowSelection(this)">
                    </td>
                    <td class="agent-name">${collection.agent_name}</td>
                    <td>
                        <input type="number" class="form-control collections-input"
                               value="${collection.gateway_amount}"
                               data-agent-id="${collection.agent_id}"
                               data-type="gateway"
                               onchange="updateTotal(this)"
                               ${hasData && !isEditMode ? 'disabled' : ''}>
                    </td>
                    <td>
                        <input type="number" class="form-control collections-input"
                               value="${collection.ryal_amount}"
                               data-agent-id="${collection.agent_id}"
                               data-type="ryal"
                               onchange="updateTotal(this)"
                               ${hasData && !isEditMode ? 'disabled' : ''}>
                    </td>
                    <td class="total-cell" id="total-${collection.agent_id}">
                        ${formatNumberEnglish(collection.total_amount)}
                    </td>
                </tr>
            `;
            tbody.innerHTML += row;
        });

        updateButtonsVisibility(true, hasData);
        isEditMode = false;
        isDeleteMode = false;
        hideSelectColumns();

    } catch (error) {
        console.error('خطأ في تحميل التحصيلات:', error);
        alert('خطأ في تحميل التحصيلات');
        updateButtonsVisibility(false, false);
    }
}

// تحديث الإجمالي
function updateTotal(input) {
    const agentId = input.dataset.agentId;
    const gatewayInput = document.querySelector(`input[data-agent-id="${agentId}"][data-type="gateway"]`);
    const ryalInput = document.querySelector(`input[data-agent-id="${agentId}"][data-type="ryal"]`);

    const gatewayAmount = parseFloat(gatewayInput.value) || 0;
    const ryalAmount = parseFloat(ryalInput.value) || 0;
    const total = gatewayAmount + ryalAmount;

    document.getElementById(`total-${agentId}`).textContent = formatNumberEnglish(total);
}

// تحديث رؤية الأزرار
function updateButtonsVisibility(dateSelected, hasExistingData) {
    const addBtn = document.getElementById('addCollectionBtn');
    const editBtn = document.getElementById('editCollectionBtn');
    const deleteBtn = document.getElementById('deleteCollectionBtn');
    const saveBtn = document.getElementById('saveBtn');
    const cancelBtn = document.getElementById('cancelBtn');
    const deleteSelectedBtn = document.getElementById('deleteSelectedBtn');

    // الأزرار الرئيسية دائماً مرئية عند تحديد تاريخ
    if (dateSelected) {
        addBtn.style.display = 'inline-block';
        editBtn.style.display = hasExistingData ? 'inline-block' : 'none';
        deleteBtn.style.display = hasExistingData ? 'inline-block' : 'none';
    } else {
        addBtn.style.display = 'none';
        editBtn.style.display = 'none';
        deleteBtn.style.display = 'none';
    }

    // أزرار الحفظ والإلغاء
    if (isEditMode) {
        saveBtn.style.display = 'inline-block';
        cancelBtn.style.display = 'inline-block';
        addBtn.style.display = 'none';
        editBtn.style.display = 'none';
        deleteBtn.style.display = 'none';
    } else {
        saveBtn.style.display = 'none';
        cancelBtn.style.display = 'none';
    }

    // زر حذف المحدد
    deleteSelectedBtn.style.display = isDeleteMode ? 'inline-block' : 'none';
}

// إضافة تحصيل جديد
async function addNewCollection() {
    const date = document.getElementById('collectionDate').value;

    if (!date) {
        alert('يرجى تحديد التاريخ أولاً');
        return;
    }

    try {
        // إضافة التاريخ إلى جدول الأيام إذا لم يكن موجوداً
        await fetch('/api/days', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ date })
        });

        // تفعيل وضع الإدخال
        const inputs = document.querySelectorAll('.collections-input');
        inputs.forEach(input => {
            input.disabled = false;
            input.value = 0;
        });

        // تحديث الإجماليات
        inputs.forEach(input => {
            if (input.dataset.type === 'gateway') {
                updateTotal(input);
            }
        });

        isEditMode = true;
        hasData = false;
        isDeleteMode = false;
        hideSelectColumns();
        updateButtonsVisibility(true, false);

    } catch (error) {
        console.error('خطأ في إضافة التحصيل الجديد:', error);
        alert('خطأ في إضافة التحصيل الجديد');
    }
}

// تفعيل وضع التحرير
function enableEdit() {
    const date = document.getElementById('collectionDate').value;

    if (!date) {
        alert('يرجى تحديد التاريخ أولاً');
        return;
    }

    if (!hasData) {
        alert('لا توجد بيانات للتعديل');
        return;
    }

    const inputs = document.querySelectorAll('.collections-input');
    inputs.forEach(input => {
        input.disabled = false;
    });

    isEditMode = true;
    isDeleteMode = false;
    hideSelectColumns();
    updateButtonsVisibility(true, true);
}

// إلغاء التحرير
function cancelEdit() {
    isEditMode = false;
    isDeleteMode = false;
    hideSelectColumns();
    loadCollections(); // إعادة تحميل البيانات الأصلية
}

// تفعيل وضع الحذف
function enableDelete() {
    const date = document.getElementById('collectionDate').value;

    if (!date) {
        alert('يرجى تحديد التاريخ أولاً');
        return;
    }

    if (!hasData) {
        alert('لا توجد بيانات للحذف');
        return;
    }

    isDeleteMode = true;
    showSelectColumns();
    updateButtonsVisibility(true, hasData);
}

// إظهار أعمدة التحديد
function showSelectColumns() {
    document.getElementById('selectAllHeader').style.display = 'table-cell';
    document.querySelectorAll('.select-column').forEach(cell => {
        cell.style.display = 'table-cell';
    });
}

// إخفاء أعمدة التحديد
function hideSelectColumns() {
    document.getElementById('selectAllHeader').style.display = 'none';
    document.querySelectorAll('.select-column').forEach(cell => {
        cell.style.display = 'none';
    });

    // إلغاء تحديد جميع المربعات
    document.getElementById('selectAll').checked = false;
    document.querySelectorAll('.row-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });

    // إزالة تنسيق الصفوف المحددة
    document.querySelectorAll('.selected-row').forEach(row => {
        row.classList.remove('selected-row');
    });
}

// تحديد/إلغاء تحديد الكل
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.row-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
        updateRowSelection(checkbox);
    });
}

// تحديث تحديد الصف
function updateRowSelection(checkbox) {
    const row = checkbox.closest('tr');

    if (checkbox.checked) {
        row.classList.add('selected-row');
    } else {
        row.classList.remove('selected-row');
        document.getElementById('selectAll').checked = false;
    }

    // التحقق من تحديد جميع الصفوف
    const allCheckboxes = document.querySelectorAll('.row-checkbox');
    const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');

    if (allCheckboxes.length === checkedBoxes.length && allCheckboxes.length > 0) {
        document.getElementById('selectAll').checked = true;
    }
}

// حذف المحدد
async function deleteSelected() {
    const checkedBoxes = document.querySelectorAll('.row-checkbox:checked');

    if (checkedBoxes.length === 0) {
        alert('يرجى تحديد عنصر واحد على الأقل للحذف');
        return;
    }

    const agentNames = [];
    checkedBoxes.forEach(checkbox => {
        const row = checkbox.closest('tr');
        const agentName = row.querySelector('.agent-name').textContent;
        agentNames.push(agentName);
    });

    const confirmMessage = `هل أنت متأكد من حذف التحصيلات للوكلاء التاليين؟\n${agentNames.join('\n')}`;

    if (!confirm(confirmMessage)) {
        return;
    }

    const date = document.getElementById('collectionDate').value;
    const agentIds = Array.from(checkedBoxes).map(checkbox => checkbox.dataset.agentId);

    try {
        const response = await fetch('/api/collections/delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ date, agentIds })
        });

        if (response.ok) {
            showAlert('تم حذف التحصيلات المحددة بنجاح', 'success');
            isDeleteMode = false;
            hideSelectColumns();
            loadCollections();
        } else {
            throw new Error('فشل في حذف التحصيلات');
        }
    } catch (error) {
        console.error('خطأ في حذف التحصيلات:', error);
        alert('خطأ في حذف التحصيلات');
    }
}

// حفظ التحصيلات
async function saveCollections() {
    const date = document.getElementById('collectionDate').value;

    if (!date) {
        alert('يرجى تحديد التاريخ');
        return;
    }

    const collections = [];
    const inputs = document.querySelectorAll('.collections-input');

    // تجميع البيانات حسب الوكيل
    const agentData = {};
    inputs.forEach(input => {
        const agentId = input.dataset.agentId;
        const type = input.dataset.type;
        const amount = parseFloat(input.value) || 0;

        if (!agentData[agentId]) {
            agentData[agentId] = { agent_id: agentId, gateway_amount: 0, ryal_amount: 0 };
        }

        if (type === 'gateway') {
            agentData[agentId].gateway_amount = amount;
        } else if (type === 'ryal') {
            agentData[agentId].ryal_amount = amount;
        }
    });

    Object.values(agentData).forEach(data => {
        collections.push(data);
    });

    try {
        const response = await fetch('/api/collections', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ date, collections })
        });

        if (response.ok) {
            showAlert('تم حفظ التحصيلات بنجاح', 'success');

            // تعطيل الحقول وتحديث الحالة
            inputs.forEach(input => {
                input.disabled = true;
            });

            hasData = true;
            isEditMode = false;
            updateButtonsVisibility(true, true);

        } else {
            throw new Error('فشل في حفظ التحصيلات');
        }
    } catch (error) {
        console.error('خطأ في حفظ التحصيلات:', error);
        alert('خطأ في حفظ التحصيلات');
    }
}

// تقرير يومي
async function generateDailyReport() {
    const date = document.getElementById('dailyReportDate').value;

    if (!date) {
        alert('يرجى تحديد التاريخ');
        return;
    }

    try {
        const response = await fetch(`/api/reports/daily/${date}`);
        const data = await response.json();

        let html = `
            <div class="table-responsive">
                <table class="table table-bordered report-table">
                    <thead>
                        <tr>
                            <th>اسم الوكيل</th>
                            <th>البوابة</th>
                            <th>ريال موبايل</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        let totalGateway = 0;
        let totalRyal = 0;
        let grandTotal = 0;

        data.forEach(row => {
            totalGateway += parseFloat(row.gateway_amount);
            totalRyal += parseFloat(row.ryal_amount);
            grandTotal += parseFloat(row.total_amount);

            html += `
                <tr>
                    <td class="report-agent-name">${row.agent_name}</td>
                    <td class="number-cell">${formatNumberEnglish(row.gateway_amount)}</td>
                    <td class="number-cell">${formatNumberEnglish(row.ryal_amount)}</td>
                    <td class="number-cell large-number">${formatNumberEnglish(row.total_amount)}</td>
                </tr>
            `;
        });

        html += `
                    <tr class="total-row">
                        <td><strong>الإجمالي</strong></td>
                        <td class="number-cell"><strong>${formatNumberEnglish(totalGateway)}</strong></td>
                        <td class="number-cell"><strong>${formatNumberEnglish(totalRyal)}</strong></td>
                        <td class="number-cell"><strong>${formatNumberEnglish(grandTotal)}</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>
        `;

        document.getElementById('reportTitle').textContent = `التقرير اليومي - ${date}`;
        document.getElementById('reportContent').innerHTML = html;
        document.getElementById('reportResults').style.display = 'block';

    } catch (error) {
        console.error('خطأ في إنشاء التقرير:', error);
        alert('خطأ في إنشاء التقرير');
    }
}

// تقرير حسب الفترة
async function generatePeriodReport() {
    const fromDate = document.getElementById('fromDate').value;
    const toDate = document.getElementById('toDate').value;
    const summary = document.getElementById('summaryReport').checked;

    if (!fromDate || !toDate) {
        alert('يرجى تحديد الفترة');
        return;
    }

    try {
        // إضافة timestamp لتجنب cache المتصفح
        const timestamp = new Date().getTime();
        const response = await fetch(`/api/reports/period?from_date=${fromDate}&to_date=${toDate}&summary=${summary}&_t=${timestamp}`);
        const data = await response.json();

        // تتبع البيانات للتشخيص
        console.log('البيانات المستلمة من الخادم:', data.slice(0, 2));

        let html = '';

        if (summary) {
            // تقرير إجمالي - يعرض إجمالي كل وكيل للفترة المحددة
            // نفس الإجماليات التي تظهر في نهاية التقرير التفصيلي
            html = `
                <div class="table-responsive">
                    <table class="table table-bordered report-table">
                        <thead>
                            <tr>
                                <th>اسم الوكيل</th>
                                <th>إجمالي البوابة</th>
                                <th>إجمالي ريال موبايل</th>
                                <th>الإجمالي العام</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            let totalGateway = 0;
            let totalRyal = 0;
            let grandTotal = 0;

            data.forEach(row => {
                totalGateway += parseFloat(row.total_gateway);
                totalRyal += parseFloat(row.total_ryal);
                grandTotal += parseFloat(row.grand_total);

                html += `
                    <tr>
                        <td class="report-agent-name">${row.agent_name}</td>
                        <td class="number-cell">${formatNumberEnglish(row.total_gateway)}</td>
                        <td class="number-cell">${formatNumberEnglish(row.total_ryal)}</td>
                        <td class="number-cell large-number">${formatNumberEnglish(row.grand_total)}</td>
                    </tr>
                `;
            });

            // إجمالي عام للفترة - نفس الإجمالي الذي يظهر في التقرير التفصيلي
            html += `
                        <tr class="total-row">
                            <td><strong>الإجمالي العام للفترة</strong></td>
                            <td class="number-cell"><strong>${formatNumberEnglish(totalGateway)}</strong></td>
                            <td class="number-cell"><strong>${formatNumberEnglish(totalRyal)}</strong></td>
                            <td class="number-cell"><strong>${formatNumberEnglish(grandTotal)}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            `;

            document.getElementById('reportTitle').textContent = `التقرير الإجمالي من ${fromDate} إلى ${toDate}`;
        } else {
            // تقرير مفصل
            // تجميع البيانات حسب التاريخ والوكيل
            const groupedData = {};
            const agents = new Set();

            data.forEach(row => {
                // التأكد من تنسيق التاريخ بشكل صحيح
                let formattedDate = row.date_id;

                // إذا كان التاريخ ما زال بالصيغة الطويلة، قم بتنسيقه
                if (formattedDate && formattedDate.includes('T')) {
                    console.log('تنسيق التاريخ من صيغة ISO:', formattedDate);
                    formattedDate = formatDateOnly(formattedDate);
                    console.log('التاريخ بعد التنسيق:', formattedDate);
                } else if (formattedDate && formattedDate.includes('GMT')) {
                    console.log('تنسيق التاريخ من صيغة GMT:', formattedDate);
                    formattedDate = formatDateOnly(formattedDate);
                    console.log('التاريخ بعد التنسيق:', formattedDate);
                } else {
                    console.log('التاريخ بالصيغة الصحيحة مسبقاً:', formattedDate);
                }

                if (!groupedData[formattedDate]) {
                    groupedData[formattedDate] = {};
                }
                groupedData[formattedDate][row.agent_name] = {
                    gateway: row.gateway_amount,
                    ryal: row.ryal_amount
                };
                agents.add(row.agent_name);
            });

            const agentsList = Array.from(agents).sort();

            html = `
                <div class="table-responsive detailed-report">
                    <table class="table table-bordered report-table">
                        <thead class="sticky-header">
                            <tr>
                                <th rowspan="2">التاريخ</th>
            `;

            agentsList.forEach((agent, index) => {
                const colorClass = `agent-color-${(index % 11) + 1}`;
                html += `<th colspan="2" class="${colorClass}">${agent}</th>`;
            });

            html += `
                            </tr>
                            <tr>
            `;

            agentsList.forEach((agent, index) => {
                const colorClass = `agent-color-${(index % 11) + 1}`;
                html += `<th class="${colorClass}">البوابة</th><th class="${colorClass}">ريال موبايل</th>`;
            });

            html += `
                            </tr>
                        </thead>
                        <tbody>
            `;

            const totals = {};
            agentsList.forEach(agent => {
                totals[agent] = { gateway: 0, ryal: 0 };
            });

            // ترتيب التواريخ بشكل صحيح
            Object.keys(groupedData).sort((a, b) => new Date(a) - new Date(b)).forEach(date => {
                html += `<tr><td><strong>${date}</strong></td>`;

                agentsList.forEach((agent, index) => {
                    const agentData = groupedData[date][agent] || { gateway: 0, ryal: 0 };
                    totals[agent].gateway += parseFloat(agentData.gateway);
                    totals[agent].ryal += parseFloat(agentData.ryal);

                    const colorClass = `agent-color-${(index % 11) + 1}`;
                    html += `
                        <td class="number-cell ${colorClass}">${formatNumberEnglish(agentData.gateway)}</td>
                        <td class="number-cell ${colorClass}">${formatNumberEnglish(agentData.ryal)}</td>
                    `;
                });

                html += `</tr>`;
            });

            // سطر الإجمالي
            html += `<tr class="total-row"><td><strong>الإجمالي</strong></td>`;
            agentsList.forEach((agent, index) => {
                const colorClass = `agent-color-${(index % 11) + 1}`;
                html += `
                    <td class="number-cell ${colorClass}"><strong>${formatNumberEnglish(totals[agent].gateway)}</strong></td>
                    <td class="number-cell ${colorClass}"><strong>${formatNumberEnglish(totals[agent].ryal)}</strong></td>
                `;
            });
            html += `</tr>`;

            html += `
                    </tbody>
                </table>
            </div>
            `;

            document.getElementById('reportTitle').textContent = `التقرير المفصل من ${fromDate} إلى ${toDate}`;
        }

        document.getElementById('reportContent').innerHTML = html;
        document.getElementById('reportResults').style.display = 'block';

    } catch (error) {
        console.error('خطأ في إنشاء التقرير:', error);
        alert('خطأ في إنشاء التقرير');
    }
}

// دوال مساعدة
function formatNumber(num) {
    return parseFloat(num).toLocaleString('ar-SA', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    });
}

function formatNumberEnglish(num) {
    return parseFloat(num).toLocaleString('en-US', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    });
}

// دالة تنسيق التاريخ (عرض التاريخ فقط بدون الوقت)
function formatDateOnly(dateString) {
    if (!dateString) return '';

    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
}

function setTodayDate(elementId = 'collectionDate') {
    const today = new Date();
    const formattedDate = formatDateOnly(today);
    document.getElementById(elementId).value = formattedDate;
}

function setDateRange() {
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);

    document.getElementById('fromDate').value = formatDateOnly(firstDay);
    document.getElementById('toDate').value = formatDateOnly(today);
}

function showAlert(message, type) {
    // يمكن تحسين هذه الدالة لاحقاً لعرض تنبيهات أفضل
    alert(message);
}

// تحديث نص معلومات التقرير
function updateReportTypeInfo() {
    const summaryCheckbox = document.getElementById('summaryReport');
    const infoText = document.getElementById('reportTypeInfo');

    if (summaryCheckbox.checked) {
        infoText.textContent = 'تقرير إجمالي: عرض إجمالي الفترة لكل وكيل';
    } else {
        infoText.textContent = 'تقرير تفصيلي: عرض البيانات يوم بيوم';
    }
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    showSection('agents');

    // إضافة مستمع لتغيير مربع التحديد
    const summaryCheckbox = document.getElementById('summaryReport');
    if (summaryCheckbox) {
        summaryCheckbox.addEventListener('change', updateReportTypeInfo);
        updateReportTypeInfo(); // تحديث النص الأولي
    }
});
