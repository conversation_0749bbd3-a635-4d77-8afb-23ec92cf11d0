// متغيرات عامة
let currentEditId = null;
let currentEditType = null;

// عرض القسم المحدد
function showSection(sectionName) {
    // إخفاء جميع الأقسام
    document.querySelectorAll('.section').forEach(section => {
        section.style.display = 'none';
    });
    
    // عرض القسم المحدد
    document.getElementById(sectionName + '-section').style.display = 'block';
    
    // تحديث الأزرار
    document.querySelectorAll('.navbar-nav .btn').forEach(btn => {
        btn.classList.remove('btn-light');
        btn.classList.add('btn-outline-light');
    });
    event.target.classList.remove('btn-outline-light');
    event.target.classList.add('btn-light');
    
    // تحميل البيانات حسب القسم
    switch(sectionName) {
        case 'agents':
            loadAgents();
            break;
        case 'users':
            loadUsers();
            break;
        case 'collections':
            setTodayDate();
            break;
        case 'reports':
            setTodayDate('dailyReportDate');
            setDateRange();
            break;
    }
}

// تحميل الوكلاء
async function loadAgents() {
    try {
        const response = await fetch('/api/agents');
        const agents = await response.json();
        
        const tbody = document.getElementById('agentsTable');
        tbody.innerHTML = '';
        
        agents.forEach(agent => {
            const row = `
                <tr>
                    <td>${agent.agent_id}</td>
                    <td>${agent.agent_name}</td>
                    <td>
                        <button class="btn btn-warning btn-sm me-1" onclick="editAgent(${agent.agent_id}, '${agent.agent_name}')">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="deleteAgent(${agent.agent_id})">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </td>
                </tr>
            `;
            tbody.innerHTML += row;
        });
    } catch (error) {
        console.error('خطأ في تحميل الوكلاء:', error);
        alert('خطأ في تحميل الوكلاء');
    }
}

// إضافة وكيل
async function addAgent() {
    const agentName = document.getElementById('agentName').value.trim();
    
    if (!agentName) {
        alert('يرجى إدخال اسم الوكيل');
        return;
    }
    
    try {
        const response = await fetch('/api/agents', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ agent_name: agentName })
        });
        
        if (response.ok) {
            document.getElementById('agentName').value = '';
            loadAgents();
            showAlert('تم إضافة الوكيل بنجاح', 'success');
        } else {
            throw new Error('فشل في إضافة الوكيل');
        }
    } catch (error) {
        console.error('خطأ في إضافة الوكيل:', error);
        alert('خطأ في إضافة الوكيل');
    }
}

// تعديل وكيل
function editAgent(id, name) {
    currentEditId = id;
    currentEditType = 'agent';
    
    document.getElementById('editModalTitle').textContent = 'تعديل الوكيل';
    document.getElementById('editModalBody').innerHTML = `
        <div class="mb-3">
            <label class="form-label">اسم الوكيل:</label>
            <input type="text" id="editAgentName" class="form-control" value="${name}">
        </div>
    `;
    
    document.getElementById('saveEditBtn').onclick = saveAgentEdit;
    new bootstrap.Modal(document.getElementById('editModal')).show();
}

// حفظ تعديل الوكيل
async function saveAgentEdit() {
    const agentName = document.getElementById('editAgentName').value.trim();
    
    if (!agentName) {
        alert('يرجى إدخال اسم الوكيل');
        return;
    }
    
    try {
        const response = await fetch(`/api/agents/${currentEditId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ agent_name: agentName })
        });
        
        if (response.ok) {
            bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
            loadAgents();
            showAlert('تم تحديث الوكيل بنجاح', 'success');
        } else {
            throw new Error('فشل في تحديث الوكيل');
        }
    } catch (error) {
        console.error('خطأ في تحديث الوكيل:', error);
        alert('خطأ في تحديث الوكيل');
    }
}

// حذف وكيل
async function deleteAgent(id) {
    if (!confirm('هل أنت متأكد من حذف هذا الوكيل؟')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/agents/${id}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            loadAgents();
            showAlert('تم حذف الوكيل بنجاح', 'success');
        } else {
            throw new Error('فشل في حذف الوكيل');
        }
    } catch (error) {
        console.error('خطأ في حذف الوكيل:', error);
        alert('خطأ في حذف الوكيل');
    }
}

// تحميل المستخدمين
async function loadUsers() {
    try {
        const response = await fetch('/api/users');
        const users = await response.json();
        
        const tbody = document.getElementById('usersTable');
        tbody.innerHTML = '';
        
        users.forEach(user => {
            const row = `
                <tr>
                    <td>${user.user_id}</td>
                    <td>${user.employee_name}</td>
                    <td>${user.username}</td>
                    <td>
                        <button class="btn btn-warning btn-sm me-1" onclick="editUser(${user.user_id}, '${user.employee_name}', '${user.username}')">
                            <i class="fas fa-edit"></i> تعديل
                        </button>
                        <button class="btn btn-danger btn-sm" onclick="deleteUser(${user.user_id})">
                            <i class="fas fa-trash"></i> حذف
                        </button>
                    </td>
                </tr>
            `;
            tbody.innerHTML += row;
        });
    } catch (error) {
        console.error('خطأ في تحميل المستخدمين:', error);
        alert('خطأ في تحميل المستخدمين');
    }
}

// إضافة مستخدم
async function addUser() {
    const employeeName = document.getElementById('employeeName').value.trim();
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value.trim();
    
    if (!employeeName || !username || !password) {
        alert('يرجى ملء جميع الحقول');
        return;
    }
    
    try {
        const response = await fetch('/api/users', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ employee_name: employeeName, username, password })
        });
        
        if (response.ok) {
            document.getElementById('employeeName').value = '';
            document.getElementById('username').value = '';
            document.getElementById('password').value = '';
            loadUsers();
            showAlert('تم إضافة المستخدم بنجاح', 'success');
        } else {
            throw new Error('فشل في إضافة المستخدم');
        }
    } catch (error) {
        console.error('خطأ في إضافة المستخدم:', error);
        alert('خطأ في إضافة المستخدم');
    }
}

// تعديل مستخدم
function editUser(id, employeeName, username) {
    currentEditId = id;
    currentEditType = 'user';
    
    document.getElementById('editModalTitle').textContent = 'تعديل المستخدم';
    document.getElementById('editModalBody').innerHTML = `
        <div class="mb-3">
            <label class="form-label">اسم الموظف:</label>
            <input type="text" id="editEmployeeName" class="form-control" value="${employeeName}">
        </div>
        <div class="mb-3">
            <label class="form-label">اسم الدخول:</label>
            <input type="text" id="editUsername" class="form-control" value="${username}">
        </div>
        <div class="mb-3">
            <label class="form-label">كلمة المرور الجديدة:</label>
            <input type="password" id="editPassword" class="form-control" placeholder="اتركها فارغة للاحتفاظ بكلمة المرور الحالية">
        </div>
    `;
    
    document.getElementById('saveEditBtn').onclick = saveUserEdit;
    new bootstrap.Modal(document.getElementById('editModal')).show();
}

// حفظ تعديل المستخدم
async function saveUserEdit() {
    const employeeName = document.getElementById('editEmployeeName').value.trim();
    const username = document.getElementById('editUsername').value.trim();
    const password = document.getElementById('editPassword').value.trim();
    
    if (!employeeName || !username) {
        alert('يرجى ملء الحقول المطلوبة');
        return;
    }
    
    const updateData = { employee_name: employeeName, username };
    if (password) {
        updateData.password = password;
    }
    
    try {
        const response = await fetch(`/api/users/${currentEditId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(updateData)
        });
        
        if (response.ok) {
            bootstrap.Modal.getInstance(document.getElementById('editModal')).hide();
            loadUsers();
            showAlert('تم تحديث المستخدم بنجاح', 'success');
        } else {
            throw new Error('فشل في تحديث المستخدم');
        }
    } catch (error) {
        console.error('خطأ في تحديث المستخدم:', error);
        alert('خطأ في تحديث المستخدم');
    }
}

// حذف مستخدم
async function deleteUser(id) {
    if (!confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/users/${id}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            loadUsers();
            showAlert('تم حذف المستخدم بنجاح', 'success');
        } else {
            throw new Error('فشل في حذف المستخدم');
        }
    } catch (error) {
        console.error('خطأ في حذف المستخدم:', error);
        alert('خطأ في حذف المستخدم');
    }
}

// تحميل التحصيلات
async function loadCollections() {
    const date = document.getElementById('collectionDate').value;
    
    if (!date) {
        return;
    }
    
    try {
        const response = await fetch(`/api/collections/${date}`);
        const collections = await response.json();
        
        const tbody = document.getElementById('collectionsTable');
        tbody.innerHTML = '';
        
        collections.forEach(collection => {
            const row = `
                <tr>
                    <td>${collection.agent_name}</td>
                    <td>
                        <input type="number" class="form-control collections-input" 
                               value="${collection.gateway_amount}" 
                               data-agent-id="${collection.agent_id}" 
                               data-type="gateway"
                               onchange="updateTotal(this)">
                    </td>
                    <td>
                        <input type="number" class="form-control collections-input" 
                               value="${collection.ryal_amount}" 
                               data-agent-id="${collection.agent_id}" 
                               data-type="ryal"
                               onchange="updateTotal(this)">
                    </td>
                    <td class="total-cell" id="total-${collection.agent_id}">
                        ${formatNumber(collection.total_amount)}
                    </td>
                </tr>
            `;
            tbody.innerHTML += row;
        });
    } catch (error) {
        console.error('خطأ في تحميل التحصيلات:', error);
        alert('خطأ في تحميل التحصيلات');
    }
}

// تحديث الإجمالي
function updateTotal(input) {
    const agentId = input.dataset.agentId;
    const gatewayInput = document.querySelector(`input[data-agent-id="${agentId}"][data-type="gateway"]`);
    const ryalInput = document.querySelector(`input[data-agent-id="${agentId}"][data-type="ryal"]`);
    
    const gatewayAmount = parseFloat(gatewayInput.value) || 0;
    const ryalAmount = parseFloat(ryalInput.value) || 0;
    const total = gatewayAmount + ryalAmount;
    
    document.getElementById(`total-${agentId}`).textContent = formatNumber(total);
}

// حفظ التحصيلات
async function saveCollections() {
    const date = document.getElementById('collectionDate').value;
    
    if (!date) {
        alert('يرجى تحديد التاريخ');
        return;
    }
    
    const collections = [];
    const inputs = document.querySelectorAll('.collections-input');
    
    // تجميع البيانات حسب الوكيل
    const agentData = {};
    inputs.forEach(input => {
        const agentId = input.dataset.agentId;
        const type = input.dataset.type;
        const amount = parseFloat(input.value) || 0;
        
        if (!agentData[agentId]) {
            agentData[agentId] = { agent_id: agentId, gateway_amount: 0, ryal_amount: 0 };
        }
        
        if (type === 'gateway') {
            agentData[agentId].gateway_amount = amount;
        } else if (type === 'ryal') {
            agentData[agentId].ryal_amount = amount;
        }
    });
    
    Object.values(agentData).forEach(data => {
        collections.push(data);
    });
    
    try {
        const response = await fetch('/api/collections', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ date, collections })
        });
        
        if (response.ok) {
            showAlert('تم حفظ التحصيلات بنجاح', 'success');
        } else {
            throw new Error('فشل في حفظ التحصيلات');
        }
    } catch (error) {
        console.error('خطأ في حفظ التحصيلات:', error);
        alert('خطأ في حفظ التحصيلات');
    }
}

// تقرير يومي
async function generateDailyReport() {
    const date = document.getElementById('dailyReportDate').value;
    
    if (!date) {
        alert('يرجى تحديد التاريخ');
        return;
    }
    
    try {
        const response = await fetch(`/api/reports/daily/${date}`);
        const data = await response.json();
        
        let html = `
            <div class="table-responsive">
                <table class="table table-bordered report-table">
                    <thead>
                        <tr>
                            <th>اسم الوكيل</th>
                            <th>البوابة</th>
                            <th>ريال موبايل</th>
                            <th>الإجمالي</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        let totalGateway = 0;
        let totalRyal = 0;
        let grandTotal = 0;
        
        data.forEach(row => {
            totalGateway += parseFloat(row.gateway_amount);
            totalRyal += parseFloat(row.ryal_amount);
            grandTotal += parseFloat(row.total_amount);
            
            html += `
                <tr>
                    <td>${row.agent_name}</td>
                    <td class="number-cell">${formatNumber(row.gateway_amount)}</td>
                    <td class="number-cell">${formatNumber(row.ryal_amount)}</td>
                    <td class="number-cell large-number">${formatNumber(row.total_amount)}</td>
                </tr>
            `;
        });
        
        html += `
                    <tr class="total-row">
                        <td><strong>الإجمالي</strong></td>
                        <td class="number-cell"><strong>${formatNumber(totalGateway)}</strong></td>
                        <td class="number-cell"><strong>${formatNumber(totalRyal)}</strong></td>
                        <td class="number-cell"><strong>${formatNumber(grandTotal)}</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>
        `;
        
        document.getElementById('reportTitle').textContent = `التقرير اليومي - ${date}`;
        document.getElementById('reportContent').innerHTML = html;
        document.getElementById('reportResults').style.display = 'block';
        
    } catch (error) {
        console.error('خطأ في إنشاء التقرير:', error);
        alert('خطأ في إنشاء التقرير');
    }
}

// تقرير حسب الفترة
async function generatePeriodReport() {
    const fromDate = document.getElementById('fromDate').value;
    const toDate = document.getElementById('toDate').value;
    const summary = document.getElementById('summaryReport').checked;
    
    if (!fromDate || !toDate) {
        alert('يرجى تحديد الفترة');
        return;
    }
    
    try {
        const response = await fetch(`/api/reports/period?from_date=${fromDate}&to_date=${toDate}&summary=${summary}`);
        const data = await response.json();
        
        let html = '';
        
        if (summary) {
            // تقرير تجميعي
            html = `
                <div class="table-responsive">
                    <table class="table table-bordered report-table">
                        <thead>
                            <tr>
                                <th>اسم الوكيل</th>
                                <th>إجمالي البوابة</th>
                                <th>إجمالي ريال موبايل</th>
                                <th>الإجمالي العام</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            let totalGateway = 0;
            let totalRyal = 0;
            let grandTotal = 0;
            
            data.forEach(row => {
                totalGateway += parseFloat(row.total_gateway);
                totalRyal += parseFloat(row.total_ryal);
                grandTotal += parseFloat(row.grand_total);
                
                html += `
                    <tr>
                        <td>${row.agent_name}</td>
                        <td class="number-cell">${formatNumber(row.total_gateway)}</td>
                        <td class="number-cell">${formatNumber(row.total_ryal)}</td>
                        <td class="number-cell large-number">${formatNumber(row.grand_total)}</td>
                    </tr>
                `;
            });
            
            html += `
                        <tr class="total-row">
                            <td><strong>الإجمالي</strong></td>
                            <td class="number-cell"><strong>${formatNumber(totalGateway)}</strong></td>
                            <td class="number-cell"><strong>${formatNumber(totalRyal)}</strong></td>
                            <td class="number-cell"><strong>${formatNumber(grandTotal)}</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            `;
            
            document.getElementById('reportTitle').textContent = `التقرير التجميعي من ${fromDate} إلى ${toDate}`;
        } else {
            // تقرير مفصل
            // تجميع البيانات حسب التاريخ والوكيل
            const groupedData = {};
            const agents = new Set();
            
            data.forEach(row => {
                if (!groupedData[row.date_id]) {
                    groupedData[row.date_id] = {};
                }
                groupedData[row.date_id][row.agent_name] = {
                    gateway: row.gateway_amount,
                    ryal: row.ryal_amount
                };
                agents.add(row.agent_name);
            });
            
            const agentsList = Array.from(agents).sort();
            
            html = `
                <div class="table-responsive detailed-report">
                    <table class="table table-bordered report-table">
                        <thead class="sticky-header">
                            <tr>
                                <th rowspan="2">التاريخ</th>
            `;
            
            agentsList.forEach(agent => {
                html += `<th colspan="2">${agent}</th>`;
            });
            
            html += `
                            </tr>
                            <tr>
            `;
            
            agentsList.forEach(agent => {
                html += `<th>البوابة</th><th>ريال موبايل</th>`;
            });
            
            html += `
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            const totals = {};
            agentsList.forEach(agent => {
                totals[agent] = { gateway: 0, ryal: 0 };
            });
            
            Object.keys(groupedData).sort().forEach(date => {
                html += `<tr><td><strong>${date}</strong></td>`;
                
                agentsList.forEach(agent => {
                    const agentData = groupedData[date][agent] || { gateway: 0, ryal: 0 };
                    totals[agent].gateway += parseFloat(agentData.gateway);
                    totals[agent].ryal += parseFloat(agentData.ryal);
                    
                    html += `
                        <td class="number-cell">${formatNumber(agentData.gateway)}</td>
                        <td class="number-cell">${formatNumber(agentData.ryal)}</td>
                    `;
                });
                
                html += `</tr>`;
            });
            
            // سطر الإجمالي
            html += `<tr class="total-row"><td><strong>الإجمالي</strong></td>`;
            agentsList.forEach(agent => {
                html += `
                    <td class="number-cell"><strong>${formatNumber(totals[agent].gateway)}</strong></td>
                    <td class="number-cell"><strong>${formatNumber(totals[agent].ryal)}</strong></td>
                `;
            });
            html += `</tr>`;
            
            html += `
                    </tbody>
                </table>
            </div>
            `;
            
            document.getElementById('reportTitle').textContent = `التقرير المفصل من ${fromDate} إلى ${toDate}`;
        }
        
        document.getElementById('reportContent').innerHTML = html;
        document.getElementById('reportResults').style.display = 'block';
        
    } catch (error) {
        console.error('خطأ في إنشاء التقرير:', error);
        alert('خطأ في إنشاء التقرير');
    }
}

// دوال مساعدة
function formatNumber(num) {
    return parseFloat(num).toLocaleString('ar-SA', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    });
}

function setTodayDate(elementId = 'collectionDate') {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById(elementId).value = today;
}

function setDateRange() {
    const today = new Date();
    const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
    
    document.getElementById('fromDate').value = firstDay.toISOString().split('T')[0];
    document.getElementById('toDate').value = today.toISOString().split('T')[0];
}

function showAlert(message, type) {
    // يمكن تحسين هذه الدالة لاحقاً لعرض تنبيهات أفضل
    alert(message);
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    showSection('agents');
});
