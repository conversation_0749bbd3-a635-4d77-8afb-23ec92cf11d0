const http = require('http');

http.get('http://localhost:7445/api/debug/imported-data?date=2025-07-08', (res) => {
  let data = '';
  res.on('data', (chunk) => data += chunk);
  res.on('end', () => {
    try {
      const result = JSON.parse(data);
      
      console.log('=== البحث عن بيانات أبو اسامه ===');
      
      // البحث في بيانات البوابة
      const gatewayData = result.gateway.find(item => item.agent_name === 'أبو اسامه');
      if (gatewayData) {
        console.log('بيانات البوابة:', gatewayData);
      } else {
        console.log('❌ لا توجد بيانات بوابة لأبو اسامه');
      }
      
      // البحث في بيانات ريال موبايل
      const ryalData = result.ryal.find(item => item.agent_name === 'أبو اسامه');
      if (ryalData) {
        console.log('بيانات ريال موبايل:', ryalData);
      } else {
        console.log('❌ لا توجد بيانات ريال موبايل لأبو اسامه');
      }
      
      console.log('\n=== جميع بيانات البوابة ===');
      result.gateway.forEach(item => {
        console.log(`${item.agent_name}: ${item.amount}`);
      });
      
      console.log('\n=== جميع بيانات ريال موبايل ===');
      result.ryal.forEach(item => {
        console.log(`${item.agent_name}: ${item.amount}`);
      });
      
    } catch (err) {
      console.error('خطأ في تحليل JSON:', err);
    }
  });
}).on('error', (err) => {
  console.error('خطأ في الطلب:', err);
});
