/* تحميل الخط المخصص */
@font-face {
    font-family: '<PERSON><PERSON>';
    src: url('./fonts/<PERSON>-<PERSON>-bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

/* تنسيق عام */
body {
    font-family: '<PERSON><PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

/* تطبيق الخط على جميع العناصر */
* {
    font-family: '<PERSON>-<PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* تطبيق الخط على العناصر المهمة */
.navbar, .card, .table, .btn, .form-control, .modal {
    font-family: '<PERSON><PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* تنسيق الأقسام */
.section {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تنسيق البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    border: none;
    padding: 15px 20px;
    font-weight: bold;
}

.card-body {
    padding: 25px;
}

/* تنسيق الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 0;
}

.table thead th {
    border: none;
    font-weight: 600;
    text-align: center;
    padding: 15px;
}

.table tbody td {
    border: none;
    padding: 12px 15px;
    text-align: center;
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
    transform: scale(1.01);
    transition: all 0.3s ease;
}

/* تنسيق الأزرار */
.btn {
    border-radius: 8px;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #1e7e34);
}

.btn-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
}

.btn-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
    color: #000;
}

.btn-info {
    background: linear-gradient(45deg, #17a2b8, #138496);
}

.btn-sm {
    padding: 5px 10px;
    font-size: 0.875rem;
}

/* تنسيق النافذة المنبثقة */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: none;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.3rem;
}

/* تنسيق الحقول */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    transform: translateY(-1px);
}

/* تنسيق التقارير */
.report-table {
    font-size: 0.9rem;
}

.report-table th {
    background: linear-gradient(45deg, #343a40, #495057);
    color: white;
    text-align: center;
    padding: 12px 8px;
    font-weight: 600;
}

.report-table td {
    text-align: center;
    padding: 10px 8px;
    border: 1px solid #dee2e6;
}

.total-row {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    font-weight: bold;
}

/* تنسيق الأرقام */
.number-cell {
    font-family: 'Courier New', monospace !important;
    font-weight: bold;
    color: #495057;
    direction: ltr;
    text-align: center;
}

.positive-amount {
    color: #28a745;
}

.zero-amount {
    color: #6c757d;
}

/* تأثيرات التحويم */
.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

/* تنسيق الأيقونات */
.fas, .far {
    margin-left: 5px;
}

/* تنسيق responsive */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }

    .card-body {
        padding: 15px;
    }

    .table-responsive {
        font-size: 0.85rem;
    }

    .btn {
        padding: 6px 12px;
        font-size: 0.875rem;
    }
}

/* تنسيق خاص للتحصيلات */
.collections-input {
    width: 180px;
    text-align: center;
    font-weight: bold;
    font-size: 1rem;
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
}

.collections-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تنسيق جدول التحصيلات */
#collectionsTable th:nth-child(1) { width: 25%; } /* اسم الوكيل */
#collectionsTable th:nth-child(2) { width: 25%; } /* البوابة */
#collectionsTable th:nth-child(3) { width: 25%; } /* ريال موبايل */
#collectionsTable th:nth-child(4) { width: 25%; } /* الإجمالي */

.total-cell {
    background-color: #e3f2fd;
    font-weight: bold;
    color: #1976d2;
    font-family: 'Courier New', monospace !important;
    direction: ltr;
    text-align: center;
    font-size: 1.1rem;
    padding: 12px 8px;
}

/* تنسيق التحديد */
.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
}

/* تنسيق Modal */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    border-radius: 15px 15px 0 0;
}

/* تنسيق التنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 20px;
}

/* تنسيق Loading */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تنسيق خاص للأرقام الكبيرة */
.large-number {
    font-size: 1.1rem;
    font-weight: bold;
    color: #2c3e50;
    font-family: 'Courier New', monospace !important;
    direction: ltr;
    text-align: center;
}

/* تنسيق العناوين */
h5, h6 {
    margin-bottom: 0;
}

/* تنسيق الفواصل */
.border-primary {
    border-color: #007bff !important;
}

.border-success {
    border-color: #28a745 !important;
}

/* تنسيق خاص للتقارير المفصلة */
.detailed-report {
    max-height: 500px;
    overflow-y: auto;
}

.sticky-header {
    position: sticky;
    top: 0;
    z-index: 10;
}

/* ألوان مختلفة للوكلاء في التقارير */
.agent-color-1 { background-color: #e3f2fd !important; } /* أزرق فاتح */
.agent-color-2 { background-color: #f3e5f5 !important; } /* بنفسجي فاتح */
.agent-color-3 { background-color: #e8f5e8 !important; } /* أخضر فاتح */
.agent-color-4 { background-color: #fff3e0 !important; } /* برتقالي فاتح */
.agent-color-5 { background-color: #fce4ec !important; } /* وردي فاتح */
.agent-color-6 { background-color: #e0f2f1 !important; } /* تركوازي فاتح */
.agent-color-7 { background-color: #f9fbe7 !important; } /* أخضر ليموني فاتح */
.agent-color-8 { background-color: #fff8e1 !important; } /* أصفر فاتح */
.agent-color-9 { background-color: #efebe9 !important; } /* بني فاتح */
.agent-color-10 { background-color: #fafafa !important; } /* رمادي فاتح */
.agent-color-11 { background-color: #e1f5fe !important; } /* أزرق سماوي فاتح */

/* تحسين حدود الجداول */
.table-bordered {
    border: 2px solid #495057 !important;
    border-radius: 8px;
    overflow: hidden;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #6c757d !important;
    border-width: 1px !important;
}

/* تحسين الشبكة */
.report-table {
    border-collapse: separate;
    border-spacing: 0;
}

.report-table th,
.report-table td {
    border: 1px solid #adb5bd !important;
    position: relative;
}

.report-table th {
    border-bottom: 2px solid #495057 !important;
}

/* تأثير الحقول غير القابلة للتعديل */
.collections-input:disabled {
    background-color: #f8f9fa;
    border-color: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
}

/* تنسيق الأيقونات */
.btn i {
    font-size: 1rem;
    margin-left: 5px;
    transition: all 0.3s ease;
}

/* تحسين تنسيق الأزرار */
.btn-group-custom {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* تأثيرات الأيقونات */
.btn:hover i {
    transform: scale(1.1) rotate(5deg);
}

/* تنسيق خاص للأيقونات حسب نوع الزر */
.btn-primary i {
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.btn-warning i {
    color: #212529;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
}

.btn-success i {
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.btn-secondary i {
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* تأثير النبض للأزرار المهمة */
.btn-success {
    animation: pulse-success 2s infinite;
}

@keyframes pulse-success {
    0% { box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); }
    50% { box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3); }
    100% { box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); }
}

/* تحسين تنسيق مجموعة الأزرار */
.btn-group-collections {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.btn-group-collections .btn {
    min-width: 120px;
    justify-content: center;
}
