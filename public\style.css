/* تحميل الخط المخصص */
@font-face {
    font-family: '<PERSON><PERSON>';
    src: url('./fonts/<PERSON>-<PERSON>-bold.ttf') format('truetype');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

/* تنسيق عام */
body {
    font-family: '<PERSON><PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    margin: 0;
    padding: 0;
}

/* تطبيق الخط على جميع العناصر */
* {
    font-family: '<PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* تصميم صفحة تسجيل الدخول */
.login-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 20px;
}

.login-container {
    width: 100%;
    max-width: 450px;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    padding: 40px;
    text-align: center;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.login-header {
    margin-bottom: 30px;
}

.login-logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.login-logo i {
    font-size: 35px;
    color: white;
}

.login-header h2 {
    color: #333;
    font-weight: bold;
    margin-bottom: 10px;
    font-size: 28px;
}

.login-header p {
    color: #666;
    margin-bottom: 0;
    font-size: 16px;
}

.login-form {
    text-align: right;
}

.login-form .form-group {
    margin-bottom: 25px;
}

.login-form label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 600;
    font-size: 14px;
}

.login-form label i {
    margin-left: 8px;
    color: #667eea;
}

.login-form .form-control {
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    padding: 15px 20px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
}

.login-form .form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
}

.password-input {
    position: relative;
}

.password-toggle {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 5px;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #667eea;
}

.btn-login {
    width: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    padding: 15px;
    font-size: 16px;
    font-weight: 600;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.btn-login:active {
    transform: translateY(0);
}

.btn-login i {
    margin-left: 8px;
}

.login-error {
    background: #fee;
    border: 1px solid #fcc;
    border-radius: 8px;
    padding: 12px;
    margin-top: 20px;
    color: #c33;
    font-size: 14px;
    text-align: center;
}

.login-error i {
    margin-left: 8px;
}

.login-footer {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.login-footer p {
    color: #999;
    font-size: 12px;
    margin: 0;
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 480px) {
    .login-card {
        padding: 30px 20px;
        margin: 10px;
    }

    .login-header h2 {
        font-size: 24px;
    }

    .login-logo {
        width: 60px;
        height: 60px;
    }

    .login-logo i {
        font-size: 25px;
    }
}

/* تنسيق عمود الصلاحيات */
.permissions-column {
    max-width: 250px;
    font-size: 11px;
    line-height: 1.3;
}

.permissions-column strong {
    color: #495057;
    font-size: 10px;
}

.permissions-column br {
    margin: 2px 0;
}

/* تحسين عرض الجداول */
.table td {
    vertical-align: middle;
}

.table .badge {
    font-size: 0.75em;
}

/* تطبيق الخط على العناصر المهمة */
.navbar, .card, .table, .btn, .form-control, .modal {
    font-family: 'Khalid-Art', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* تنسيق الأقسام */
.section {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تنسيق البطاقات */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    border: none;
    padding: 15px 20px;
    font-weight: bold;
}

.card-body {
    padding: 25px;
}

/* تنسيق الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 0;
}

.table thead th {
    border: none;
    font-weight: 600;
    text-align: center;
    padding: 15px;
}

.table tbody td {
    border: none;
    padding: 12px 15px;
    text-align: center;
    vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.1);
    transform: scale(1.01);
    transition: all 0.3s ease;
}

/* تنسيق الأزرار */
.btn {
    border-radius: 8px;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #1e7e34);
}

.btn-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
}

.btn-warning {
    background: linear-gradient(45deg, #ffc107, #e0a800);
    color: #000;
}

.btn-info {
    background: linear-gradient(45deg, #17a2b8, #138496);
}

.btn-sm {
    padding: 5px 10px;
    font-size: 0.875rem;
}

/* تنسيق النافذة المنبثقة */
.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: none;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.3rem;
}

/* تنسيق الحقول */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 10px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    transform: translateY(-1px);
}

/* تنسيق التقارير */
.report-table {
    font-size: 0.9rem;
}

.report-table th {
    background: linear-gradient(45deg, #343a40, #495057);
    color: white;
    text-align: center;
    padding: 12px 8px;
    font-weight: 600;
}

.report-table td {
    text-align: center;
    padding: 10px 8px;
    border: 1px solid #dee2e6;
}

.total-row {
    background: linear-gradient(45deg, #28a745, #20c997);
    color: white;
    font-weight: bold;
}

/* تنسيق الأرقام */
.number-cell {
    font-family: 'Courier New', monospace !important;
    font-weight: bold;
    color: #495057;
    direction: ltr;
    text-align: center;
}

.positive-amount {
    color: #28a745;
}

.zero-amount {
    color: #6c757d;
}

/* تأثيرات التحويم */
.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

/* تنسيق الأيقونات */
.fas, .far {
    margin-left: 5px;
}

/* تنسيق responsive */
@media (max-width: 768px) {
    .container-fluid {
        padding: 10px;
    }

    .card-body {
        padding: 15px;
    }

    .table-responsive {
        font-size: 0.85rem;
    }

    .btn {
        padding: 6px 12px;
        font-size: 0.875rem;
    }
}

/* تنسيق خاص للتحصيلات */
.collections-input {
    width: 180px;
    text-align: center;
    font-weight: bold;
    font-size: 1rem;
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
}

/* تنسيق أسماء الوكلاء */
.agent-name {
    font-size: 1.2rem;
    font-weight: bold;
    color: #2c3e50;
}

/* تنسيق أسماء الوكلاء في التقارير */
.report-agent-name {
    font-size: 1.1rem;
    font-weight: bold;
    color: #2c3e50;
}

.collections-input:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* تنسيق جدول التحصيلات */
#collectionsTable th:nth-child(1) { width: 25%; } /* اسم الوكيل */
#collectionsTable th:nth-child(2) { width: 25%; } /* البوابة */
#collectionsTable th:nth-child(3) { width: 25%; } /* ريال موبايل */
#collectionsTable th:nth-child(4) { width: 25%; } /* الإجمالي */

.total-cell {
    background-color: #e3f2fd;
    font-weight: bold;
    color: #1976d2;
    font-family: 'Courier New', monospace !important;
    direction: ltr;
    text-align: center;
    font-size: 1.1rem;
    padding: 12px 8px;
}

/* تنسيق التحديد */
.form-check-input:checked {
    background-color: #007bff;
    border-color: #007bff;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
    background-size: 14px 14px;
    background-position: center;
    background-repeat: no-repeat;
}

/* تنسيق خاص لمربعات التحديد في الجداول */
.row-checkbox:checked {
    background-color: #28a745;
    border-color: #28a745;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='4' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
    background-size: 18px 18px;
    background-position: center;
    background-repeat: no-repeat;
    box-shadow: 0 0 0 2px rgba(40, 167, 69, 0.3);
}

/* تنسيق مربع تحديد الكل */
#selectAll:checked {
    background-color: #dc3545;
    border-color: #dc3545;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='4' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
    background-size: 18px 18px;
    background-position: center;
    background-repeat: no-repeat;
    box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.3);
}

/* تنسيق مربع التحديد في العنوان */
.card-header .form-check {
    margin-bottom: 0;
}

.card-header .form-check-input {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    margin-top: 0;
    position: relative;
}

.card-header .form-check-input:checked {
    background-color: #fff;
    border-color: #fff;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23198754' stroke-linecap='round' stroke-linejoin='round' stroke-width='4' d='m6 10 3 3 6-6'/%3e%3c/svg%3e");
    background-size: 18px 18px;
    background-position: center;
    background-repeat: no-repeat;
    box-shadow: 0 0 0 2px rgba(25, 135, 84, 0.2);
}

.card-header .form-check-label {
    font-size: 0.9rem;
    margin-right: 8px;
}

/* تحسين تنسيق العنوان مع مربع التحديد */
.card-header.d-flex {
    align-items: center;
    gap: 15px;
}

/* تنسيق التنبيه التوضيحي */
.alert-info {
    background-color: #e3f2fd;
    border-color: #bbdefb;
    color: #1976d2;
    border-radius: 8px;
}

.alert-info .fas {
    color: #1976d2;
}

/* تنسيق Modal */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    border-radius: 15px 15px 0 0;
}

/* تنسيق التنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    padding: 15px 20px;
    margin-bottom: 20px;
}

/* تنسيق Loading */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* تنسيق خاص للأرقام الكبيرة */
.large-number {
    font-size: 1.1rem;
    font-weight: bold;
    color: #2c3e50;
    font-family: 'Courier New', monospace !important;
    direction: ltr;
    text-align: center;
}

/* تنسيق العناوين */
h5, h6 {
    margin-bottom: 0;
}

/* تنسيق الفواصل */
.border-primary {
    border-color: #007bff !important;
}

.border-success {
    border-color: #28a745 !important;
}

/* تنسيق خاص للتقارير المفصلة */
.detailed-report {
    max-height: 500px;
    overflow-y: auto;
}

.sticky-header {
    position: sticky;
    top: 0;
    z-index: 10;
}

/* ألوان مختلفة للوكلاء في التقارير */
.agent-color-1 { background-color: #e3f2fd !important; } /* أزرق فاتح */
.agent-color-2 { background-color: #f3e5f5 !important; } /* بنفسجي فاتح */
.agent-color-3 { background-color: #e8f5e8 !important; } /* أخضر فاتح */
.agent-color-4 { background-color: #fff3e0 !important; } /* برتقالي فاتح */
.agent-color-5 { background-color: #fce4ec !important; } /* وردي فاتح */
.agent-color-6 { background-color: #e0f2f1 !important; } /* تركوازي فاتح */
.agent-color-7 { background-color: #f9fbe7 !important; } /* أخضر ليموني فاتح */
.agent-color-8 { background-color: #fff8e1 !important; } /* أصفر فاتح */
.agent-color-9 { background-color: #efebe9 !important; } /* بني فاتح */
.agent-color-10 { background-color: #fafafa !important; } /* رمادي فاتح */
.agent-color-11 { background-color: #e1f5fe !important; } /* أزرق سماوي فاتح */

/* تحسين حدود الجداول */
.table-bordered {
    border: 2px solid #495057 !important;
    border-radius: 8px;
    overflow: hidden;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #6c757d !important;
    border-width: 1px !important;
}

/* تحسين الشبكة */
.report-table {
    border-collapse: separate;
    border-spacing: 0;
}

.report-table th,
.report-table td {
    border: 1px solid #adb5bd !important;
    position: relative;
}

.report-table th {
    border-bottom: 2px solid #495057 !important;
}

/* تأثير الحقول غير القابلة للتعديل */
.collections-input:disabled {
    background-color: #f8f9fa;
    border-color: #e9ecef;
    color: #6c757d;
    cursor: not-allowed;
}

/* تنسيق الأيقونات */
.btn i {
    font-size: 1rem;
    margin-left: 5px;
    transition: all 0.3s ease;
}

/* تحسين تنسيق الأزرار */
.btn-group-custom {
    display: flex;
    gap: 8px;
    align-items: center;
}

/* تأثيرات الأيقونات */
.btn:hover i {
    transform: scale(1.1) rotate(5deg);
}

/* تنسيق خاص للأيقونات حسب نوع الزر */
.btn-primary i {
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.btn-warning i {
    color: #212529;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
}

.btn-success i {
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.btn-secondary i {
    color: #fff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* تأثير النبض للأزرار المهمة */
.btn-success {
    animation: pulse-success 2s infinite;
}

@keyframes pulse-success {
    0% { box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); }
    50% { box-shadow: 0 4px 8px rgba(40, 167, 69, 0.3); }
    100% { box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); }
}

/* تحسين تنسيق مجموعة الأزرار */
.btn-group-collections {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.btn-group-collections .btn {
    min-width: 120px;
    justify-content: center;
}

/* تنسيق مربعات التحديد */
.row-checkbox {
    transform: scale(1.2);
    margin: 0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.row-checkbox:hover {
    transform: scale(1.3);
    box-shadow: 0 0 10px rgba(40, 167, 69, 0.3);
}

/* تأثيرات تفاعلية لجميع مربعات التحديد */
.form-check-input {
    transition: all 0.3s ease;
    cursor: pointer;
}

.form-check-input:hover {
    transform: scale(1.1);
    box-shadow: 0 0 8px rgba(0, 123, 255, 0.3);
}

.form-check-input:focus {
    box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25);
}

/* تأثير خاص لمربع تحديد الكل */
#selectAll:hover {
    transform: scale(1.1);
    box-shadow: 0 0 10px rgba(220, 53, 69, 0.3);
}

.select-column {
    width: 50px;
    text-align: center;
    vertical-align: middle;
}

/* تنسيق الصفوف المحددة */
.selected-row {
    background-color: #fff3cd !important;
    border-color: #ffeaa7 !important;
}

/* تنسيق زر حذف المحدد */
.btn-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
}

.btn-danger:hover {
    background: linear-gradient(45deg, #c82333, #a71e2a);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
}

/* تنسيق لوحة التحكم */
.dashboard-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    border-radius: 15px;
    overflow: hidden;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.dashboard-card .card-body {
    padding: 1.5rem;
}

.dashboard-card h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 0;
}

.dashboard-card h6 {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 0.5rem;
}

/* تنسيق الرسوم البيانية */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* تنسيق جدول الأداء */
.performance-table {
    font-size: 0.9rem;
}

.performance-table th {
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
}

.performance-table td {
    text-align: center;
    vertical-align: middle;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
}

.status-excellent {
    background-color: #28a745;
    color: white;
}

.status-good {
    background-color: #17a2b8;
    color: white;
}

.status-average {
    background-color: #ffc107;
    color: #212529;
}

.status-poor {
    background-color: #dc3545;
    color: white;
}

/* تحسينات للأرقام */
.number-large {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
}

.number-currency {
    color: #28a745;
    font-weight: bold;
}

/* تأثيرات التحميل */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* تنسيق الإحصائيات السريعة */
.stat-card {
    background: linear-gradient(135deg, var(--bs-primary), var(--bs-primary-dark));
    border: none;
    border-radius: 15px;
    color: white;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-card.bg-success {
    background: linear-gradient(135deg, #28a745, #20c997);
}

.stat-card.bg-info {
    background: linear-gradient(135deg, #17a2b8, #20c997);
}

.stat-card.bg-warning {
    background: linear-gradient(135deg, #ffc107, #fd7e14);
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    .dashboard-card h3 {
        font-size: 2rem;
    }

    .chart-container {
        height: 250px;
    }

    .performance-table {
        font-size: 0.8rem;
    }
}
