const { Pool } = require('pg');

// إعداد قاعدة البيانات
const pool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'agent',
    password: 'yemen123',
    port: 5432,
});

async function testFixedQuery() {
    try {
        console.log('اختبار الاستعلام المحسن للتقرير الإجمالي...');
        console.log('الفترة: 2025-07-13 إلى 2025-07-14');
        
        // تشغيل الاستعلام الجديد المحسن
        const result = await pool.query(`
            SELECT 
                a.agent_name,
                COALESCE(gateway_totals.total_gateway, 0) as total_gateway,
                COALESCE(ryal_totals.total_ryal, 0) as total_ryal,
                (COALESCE(gateway_totals.total_gateway, 0) + COALESCE(ryal_totals.total_ryal, 0)) as grand_total
            FROM agents a
            LEFT JOIN (
                SELECT agent_id, SUM(amount) as total_gateway
                FROM gateway_collections 
                WHERE date_id BETWEEN $1 AND $2
                GROUP BY agent_id
            ) gateway_totals ON a.agent_id = gateway_totals.agent_id
            LEFT JOIN (
                SELECT agent_id, SUM(amount) as total_ryal
                FROM ryal_mobile 
                WHERE date_id BETWEEN $1 AND $2
                GROUP BY agent_id
            ) ryal_totals ON a.agent_id = ryal_totals.agent_id
            WHERE a.agent_name = 'أبو اسامه'
            ORDER BY a.agent_name
        `, ['2025-07-13', '2025-07-14']);
        
        if (result.rows.length > 0) {
            const row = result.rows[0];
            console.log(`\nنتيجة الاستعلام المحسن للوكيل "${row.agent_name}":`);
            console.log(`البوابة: ${parseFloat(row.total_gateway).toLocaleString()}`);
            console.log(`ريال موبايل: ${parseFloat(row.total_ryal).toLocaleString()}`);
            console.log(`الإجمالي: ${parseFloat(row.grand_total).toLocaleString()}`);
            
            // مقارنة مع القيم المتوقعة
            const expectedGateway = 21880050;
            const expectedRyal = 68579029;
            const expectedTotal = expectedGateway + expectedRyal;
            
            console.log(`\nالقيم المتوقعة:`);
            console.log(`البوابة: ${expectedGateway.toLocaleString()}`);
            console.log(`ريال موبايل: ${expectedRyal.toLocaleString()}`);
            console.log(`الإجمالي: ${expectedTotal.toLocaleString()}`);
            
            console.log(`\nالمقارنة:`);
            console.log(`البوابة: ${parseFloat(row.total_gateway) === expectedGateway ? '✓ صحيح' : '✗ خطأ'}`);
            console.log(`ريال موبايل: ${parseFloat(row.total_ryal) === expectedRyal ? '✓ صحيح' : '✗ خطأ'}`);
            console.log(`الإجمالي: ${parseFloat(row.grand_total) === expectedTotal ? '✓ صحيح' : '✗ خطأ'}`);
        } else {
            console.log('لم يتم العثور على بيانات للوكيل');
        }
        
        // اختبار جميع الوكلاء للفترة
        console.log('\n--- اختبار جميع الوكلاء للفترة ---');
        const allResult = await pool.query(`
            SELECT 
                a.agent_name,
                COALESCE(gateway_totals.total_gateway, 0) as total_gateway,
                COALESCE(ryal_totals.total_ryal, 0) as total_ryal,
                (COALESCE(gateway_totals.total_gateway, 0) + COALESCE(ryal_totals.total_ryal, 0)) as grand_total
            FROM agents a
            LEFT JOIN (
                SELECT agent_id, SUM(amount) as total_gateway
                FROM gateway_collections 
                WHERE date_id BETWEEN $1 AND $2
                GROUP BY agent_id
            ) gateway_totals ON a.agent_id = gateway_totals.agent_id
            LEFT JOIN (
                SELECT agent_id, SUM(amount) as total_ryal
                FROM ryal_mobile 
                WHERE date_id BETWEEN $1 AND $2
                GROUP BY agent_id
            ) ryal_totals ON a.agent_id = ryal_totals.agent_id
            ORDER BY a.agent_name
        `, ['2025-07-13', '2025-07-14']);
        
        allResult.rows.forEach(row => {
            if (parseFloat(row.grand_total) > 0) {
                console.log(`${row.agent_name}: البوابة=${parseFloat(row.total_gateway).toLocaleString()}, ريال=${parseFloat(row.total_ryal).toLocaleString()}, الإجمالي=${parseFloat(row.grand_total).toLocaleString()}`);
            }
        });
        
    } catch (err) {
        console.error('خطأ في اختبار الاستعلام:', err);
    } finally {
        await pool.end();
    }
}

// تشغيل الاختبار
testFixedQuery();
