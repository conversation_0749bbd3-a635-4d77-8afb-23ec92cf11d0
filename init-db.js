const { Pool } = require('pg');
const fs = require('fs');

// إعداد قاعدة البيانات
const pool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'agent',
    password: 'yemen123',
    port: 5432,
});

async function initDatabase() {
    try {
        console.log('بدء تهيئة قاعدة البيانات...');
        
        // قراءة وتنفيذ ملف SQL
        const sqlScript = fs.readFileSync('database.sql', 'utf8');
        await pool.query(sqlScript);
        console.log('✓ تم إنشاء الجداول بنجاح');
        
        console.log('تم تهيئة قاعدة البيانات بنجاح!');
        
    } catch (err) {
        console.error('خطأ في تهيئة قاعدة البيانات:', err.message);
        console.error('تفاصيل الخطأ:', err);
    } finally {
        await pool.end();
    }
}

// تشغيل التهيئة
initDatabase();
