const { Pool } = require('pg');

// إعداد قاعدة البيانات
const pool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'agent',
    password: 'yemen123',
    port: 5432,
});

async function fixAsesDuplicate() {
    try {
        console.log('إصلاح مشكلة الوكيل المكرر "اسس"...');
        
        // البحث عن الوكيل المكرر "اسس" (بدون همزة)
        const duplicateResult = await pool.query(
            'SELECT agent_id, agent_name FROM agents WHERE agent_name = $1',
            ['اسس']
        );
        
        if (duplicateResult.rows.length > 0) {
            const duplicateId = duplicateResult.rows[0].agent_id;
            console.log(`وجد وكيل مكرر "اسس" بالمعرف: ${duplicateId}`);
            
            // التحقق من عدم وجود بيانات مرتبطة
            const gatewayCheck = await pool.query(
                'SELECT COUNT(*) as count FROM gateway_collections WHERE agent_id = $1',
                [duplicateId]
            );
            
            const ryalCheck = await pool.query(
                'SELECT COUNT(*) as count FROM ryal_mobile WHERE agent_id = $1',
                [duplicateId]
            );
            
            console.log(`البيانات المرتبطة: البوابة=${gatewayCheck.rows[0].count}، ريال=${ryalCheck.rows[0].count}`);
            
            if (gatewayCheck.rows[0].count == 0 && ryalCheck.rows[0].count == 0) {
                // حذف الوكيل المكرر
                await pool.query('DELETE FROM agents WHERE agent_id = $1', [duplicateId]);
                console.log('✓ تم حذف الوكيل المكرر "اسس" (بدون همزة)');
            } else {
                console.log('⚠️ الوكيل المكرر يحتوي على بيانات - لن يتم حذفه');
            }
        } else {
            console.log('لا يوجد وكيل مكرر "اسس"');
        }
        
        // التحقق من الوكيل الصحيح "أسس" (بهمزة)
        const correctResult = await pool.query(
            'SELECT agent_id, agent_name FROM agents WHERE agent_name = $1',
            ['أسس']
        );
        
        if (correctResult.rows.length > 0) {
            const correctId = correctResult.rows[0].agent_id;
            console.log(`✓ الوكيل الصحيح "أسس" موجود بالمعرف: ${correctId}`);
            
            // فحص البيانات
            const gatewayCount = await pool.query(
                'SELECT COUNT(*) as count FROM gateway_collections WHERE agent_id = $1',
                [correctId]
            );
            
            const ryalCount = await pool.query(
                'SELECT COUNT(*) as count FROM ryal_mobile WHERE agent_id = $1',
                [correctId]
            );
            
            console.log(`البيانات: البوابة=${gatewayCount.rows[0].count} سجل، ريال=${ryalCount.rows[0].count} سجل`);
        } else {
            console.log('❌ الوكيل الصحيح "أسس" غير موجود');
        }
        
        // عرض قائمة الوكلاء النهائية
        console.log('\n--- قائمة الوكلاء النهائية ---');
        const allAgents = await pool.query('SELECT agent_id, agent_name FROM agents ORDER BY agent_name');
        
        allAgents.rows.forEach(agent => {
            console.log(`${agent.agent_id}: ${agent.agent_name}`);
        });
        
        console.log(`\nإجمالي عدد الوكلاء: ${allAgents.rows.length}`);
        
    } catch (err) {
        console.error('خطأ في إصلاح المشكلة:', err);
    } finally {
        await pool.end();
    }
}

// تشغيل الإصلاح
fixAsesDuplicate();
