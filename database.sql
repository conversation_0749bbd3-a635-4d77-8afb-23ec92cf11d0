-- إنشا<PERSON> قاعدة البيانات والجداول

-- جدول الوكلاء
CREATE TABLE IF NOT EXISTS agents (
    agent_id SERIAL PRIMARY KEY,
    agent_name VARCHAR(100) NOT NULL UNIQUE
);

-- جدول الأيام
CREATE TABLE IF NOT EXISTS days (
    date_id DATE PRIMARY KEY
);

-- جدول البوابات (للربط بين الوكلاء والتحصيلات)
CREATE TABLE IF NOT EXISTS gateways (
    gateway_id SERIAL PRIMARY KEY,
    gateway_name VARCHAR(100) NOT NULL,
    ryal_mobile VARCHAR(100) NOT NULL
);

-- جدول التحصيلات
CREATE TABLE IF NOT EXISTS collections (
    collection_id SERIAL PRIMARY KEY,
    date_id DATE NOT NULL REFERENCES days(date_id),
    amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    gateway_id INTEGER NOT NULL REFERENCES gateways(gateway_id)
);

-- جدول البوابة (تحصيلات البوابة)
CREATE TABLE IF NOT EXISTS gateway_collections (
    id SERIAL PRIMARY KEY,
    agent_id INTEGER NOT NULL REFERENCES agents(agent_id),
    amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    date_id DATE NOT NULL REFERENCES days(date_id)
);

-- جدول ريال موبايل
CREATE TABLE IF NOT EXISTS ryal_mobile (
    id SERIAL PRIMARY KEY,
    amount DECIMAL(15,2) NOT NULL DEFAULT 0,
    agent_id INTEGER NOT NULL REFERENCES agents(agent_id),
    date_id DATE NOT NULL REFERENCES days(date_id)
);

-- جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    user_id SERIAL PRIMARY KEY,
    employee_name VARCHAR(100) NOT NULL,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(100) NOT NULL
);

-- إدراج بيانات الوكلاء
INSERT INTO agents (agent_name) VALUES 
('المامون'),
('الشرعبي'),
('عومان'),
('الكون'),
('سكافا'),
('أبو اسامه'),
('أسس'),
('الاثير'),
('المترب'),
('باتكو'),
('بران')
ON CONFLICT (agent_name) DO NOTHING;

-- إدراج المستخدم الرئيسي
INSERT INTO users (employee_name, username, password) VALUES 
('محمد الحاشدي', 'admin', 'admin123')
ON CONFLICT (username) DO NOTHING;

-- إنشاء الأيام من 2025-06-17 إلى 2025-07-14
INSERT INTO days (date_id)
SELECT generate_series('2025-06-17'::date, '2025-07-14'::date, '1 day'::interval)::date
ON CONFLICT (date_id) DO NOTHING;
