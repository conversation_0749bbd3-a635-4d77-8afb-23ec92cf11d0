const { Pool } = require('pg');

// إعداد قاعدة البيانات
const pool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'agent',
    password: 'yemen123',
    port: 5432,
});

async function checkAgentData() {
    try {
        console.log('فحص بيانات الوكيل "أبو اسامه" للفترة 2025-07-13 إلى 2025-07-14...');
        
        // الحصول على معرف الوكيل
        const agentResult = await pool.query(
            'SELECT agent_id FROM agents WHERE agent_name = $1',
            ['أبو اسامه']
        );
        
        if (agentResult.rows.length === 0) {
            console.log('الوكيل "أبو اسامه" غير موجود');
            return;
        }
        
        const agentId = agentResult.rows[0].agent_id;
        console.log(`معرف الوكيل "أبو اسامه": ${agentId}`);
        
        // فحص بيانات البوابة للفترة المحددة
        console.log('\n--- بيانات البوابة للفترة 2025-07-13 إلى 2025-07-14 ---');
        const gatewayResult = await pool.query(`
            SELECT date_id, amount 
            FROM gateway_collections 
            WHERE agent_id = $1 AND date_id BETWEEN '2025-07-13' AND '2025-07-14'
            ORDER BY date_id
        `, [agentId]);
        
        let totalGateway = 0;
        gatewayResult.rows.forEach(row => {
            console.log(`التاريخ: ${row.date_id}, المبلغ: ${row.amount}`);
            totalGateway += parseFloat(row.amount);
        });
        console.log(`إجمالي البوابة: ${totalGateway}`);
        
        // فحص بيانات ريال موبايل للفترة المحددة
        console.log('\n--- بيانات ريال موبايل للفترة 2025-07-13 إلى 2025-07-14 ---');
        const ryalResult = await pool.query(`
            SELECT date_id, amount 
            FROM ryal_mobile 
            WHERE agent_id = $1 AND date_id BETWEEN '2025-07-13' AND '2025-07-14'
            ORDER BY date_id
        `, [agentId]);
        
        let totalRyal = 0;
        ryalResult.rows.forEach(row => {
            console.log(`التاريخ: ${row.date_id}, المبلغ: ${row.amount}`);
            totalRyal += parseFloat(row.amount);
        });
        console.log(`إجمالي ريال موبايل: ${totalRyal}`);
        
        console.log(`\nالإجمالي العام: ${totalGateway + totalRyal}`);
        
        // فحص جميع بيانات الوكيل (للمقارنة)
        console.log('\n--- جميع بيانات البوابة للوكيل ---');
        const allGatewayResult = await pool.query(`
            SELECT date_id, amount 
            FROM gateway_collections 
            WHERE agent_id = $1
            ORDER BY date_id
        `, [agentId]);
        
        let allGatewayTotal = 0;
        allGatewayResult.rows.forEach(row => {
            allGatewayTotal += parseFloat(row.amount);
        });
        console.log(`إجمالي جميع بيانات البوابة: ${allGatewayTotal}`);
        
        console.log('\n--- جميع بيانات ريال موبايل للوكيل ---');
        const allRyalResult = await pool.query(`
            SELECT date_id, amount 
            FROM ryal_mobile 
            WHERE agent_id = $1
            ORDER BY date_id
        `, [agentId]);
        
        let allRyalTotal = 0;
        allRyalResult.rows.forEach(row => {
            allRyalTotal += parseFloat(row.amount);
        });
        console.log(`إجمالي جميع بيانات ريال موبايل: ${allRyalTotal}`);
        
        // تشغيل نفس الاستعلام المستخدم في التقرير
        console.log('\n--- اختبار استعلام التقرير الإجمالي ---');
        const reportResult = await pool.query(`
            SELECT 
                a.agent_name,
                COALESCE(SUM(gc.amount), 0) as total_gateway,
                COALESCE(SUM(rm.amount), 0) as total_ryal,
                (COALESCE(SUM(gc.amount), 0) + COALESCE(SUM(rm.amount), 0)) as grand_total
            FROM agents a
            LEFT JOIN gateway_collections gc ON a.agent_id = gc.agent_id 
                AND gc.date_id BETWEEN $1 AND $2
            LEFT JOIN ryal_mobile rm ON a.agent_id = rm.agent_id 
                AND rm.date_id BETWEEN $1 AND $2
            WHERE a.agent_name = 'أبو اسامه'
            GROUP BY a.agent_id, a.agent_name
        `, ['2025-07-13', '2025-07-14']);
        
        if (reportResult.rows.length > 0) {
            const row = reportResult.rows[0];
            console.log(`نتيجة استعلام التقرير:`);
            console.log(`البوابة: ${row.total_gateway}`);
            console.log(`ريال موبايل: ${row.total_ryal}`);
            console.log(`الإجمالي: ${row.grand_total}`);
        }
        
    } catch (err) {
        console.error('خطأ في فحص البيانات:', err);
    } finally {
        await pool.end();
    }
}

// تشغيل الفحص
checkAgentData();
