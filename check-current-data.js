const http = require('http');

http.get('http://localhost:7445/api/collections/2025-07-08', (res) => {
  let data = '';
  res.on('data', (chunk) => data += chunk);
  res.on('end', () => {
    try {
      const result = JSON.parse(data);
      
      console.log('=== البيانات الحالية في النظام لتاريخ 2025-07-08 ===');
      
      // البحث عن أبو اسامه
      const abuData = result.find(item => item.agent_name === 'أبو اسامه');
      if (abuData) {
        console.log('بيانات أبو اسامه:');
        console.log('البوابة:', abuData.gateway_amount);
        console.log('ريال موبايل:', abuData.ryal_amount);
        console.log('الإجمالي:', abuData.total_amount);
      } else {
        console.log('❌ لم يتم العثور على بيانات أبو اسامه');
      }
      
      console.log('\n=== جميع البيانات ===');
      result.forEach(item => {
        console.log(`${item.agent_name}: بوابة=${item.gateway_amount}, ريال=${item.ryal_amount}, إجمالي=${item.total_amount}`);
      });
      
    } catch (err) {
      console.error('خطأ في تحليل JSON:', err);
    }
  });
}).on('error', (err) => {
  console.error('خطأ في الطلب:', err);
});
