const http = require('http');

http.get('http://localhost:7445/api/agents', (res) => {
  let data = '';
  res.on('data', (chunk) => data += chunk);
  res.on('end', () => {
    try {
      const agents = JSON.parse(data);
      console.log('أسماء الوكلاء في قاعدة البيانات:');
      agents.forEach((agent, index) => {
        console.log(`${index + 1}. ID: ${agent.agent_id} - الاسم: "${agent.agent_name}"`);
      });
    } catch (err) {
      console.error('خطأ في تحليل JSON:', err);
    }
  });
}).on('error', (err) => {
  console.error('خطأ في الطلب:', err);
});
