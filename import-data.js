const { Pool } = require('pg');
const fs = require('fs');
const csv = require('csv-parser');

// إعداد قاعدة البيانات
const pool = new Pool({
    user: 'postgres',
    host: 'localhost',
    database: 'agent',
    password: 'yemen123',
    port: 5432,
});

// استيراد بيانات البوابة من ملف CSV
async function importGatewayData() {
    return new Promise((resolve, reject) => {
        const results = [];
        console.log('بدء استيراد بيانات البوابة...');
        
        fs.createReadStream('import_fixed.csv')
            .pipe(csv())
            .on('data', (data) => results.push(data))
            .on('end', async () => {
                try {
                    // حذف البيانات الموجودة
                    await pool.query('DELETE FROM gateway_collections');
                    console.log('تم حذف البيانات القديمة للبوابة');
                    
                    let insertedCount = 0;
                    
                    for (const row of results) {
                        const agentName = row.agent_name;
                        console.log(`معالجة بيانات الوكيل: ${agentName}`);
                        
                        // الحصول على معرف الوكيل
                        const agentResult = await pool.query(
                            'SELECT agent_id FROM agents WHERE agent_name = $1',
                            [agentName]
                        );
                        
                        if (agentResult.rows.length > 0) {
                            const agentId = agentResult.rows[0].agent_id;
                            
                            // إدراج البيانات لكل تاريخ
                            for (const [date, amount] of Object.entries(row)) {
                                if (date !== 'agent_name' && amount && amount !== '0') {
                                    const cleanAmount = parseFloat(amount.replace(/,/g, '')) || 0;
                                    if (cleanAmount > 0) {
                                        await pool.query(
                                            'INSERT INTO gateway_collections (agent_id, amount, date_id) VALUES ($1, $2, $3)',
                                            [agentId, cleanAmount, date]
                                        );
                                        insertedCount++;
                                    }
                                }
                            }
                        } else {
                            console.log(`تحذير: لم يتم العثور على الوكيل ${agentName}`);
                        }
                    }
                    
                    console.log(`✓ تم استيراد ${insertedCount} سجل من بيانات البوابة`);
                    resolve();
                } catch (err) {
                    console.error('خطأ في استيراد بيانات البوابة:', err);
                    reject(err);
                }
            })
            .on('error', (err) => {
                console.error('خطأ في قراءة ملف البوابة:', err);
                reject(err);
            });
    });
}

// استيراد بيانات ريال موبايل من ملف CSV
async function importRyalData() {
    return new Promise((resolve, reject) => {
        const results = [];
        console.log('بدء استيراد بيانات ريال موبايل...');
        
        fs.createReadStream('ryal_fixed.csv')
            .pipe(csv())
            .on('data', (data) => results.push(data))
            .on('end', async () => {
                try {
                    // حذف البيانات الموجودة
                    await pool.query('DELETE FROM ryal_mobile');
                    console.log('تم حذف البيانات القديمة لريال موبايل');
                    
                    let insertedCount = 0;
                    
                    for (const row of results) {
                        const agentName = row.agent_name;
                        console.log(`معالجة بيانات ريال موبايل للوكيل: ${agentName}`);
                        
                        // الحصول على معرف الوكيل
                        const agentResult = await pool.query(
                            'SELECT agent_id FROM agents WHERE agent_name = $1',
                            [agentName]
                        );
                        
                        if (agentResult.rows.length > 0) {
                            const agentId = agentResult.rows[0].agent_id;
                            
                            // إدراج البيانات لكل تاريخ
                            for (const [date, amount] of Object.entries(row)) {
                                if (date !== 'agent_name' && amount && amount !== '0') {
                                    const cleanAmount = parseFloat(amount.replace(/,/g, '')) || 0;
                                    if (cleanAmount > 0) {
                                        await pool.query(
                                            'INSERT INTO ryal_mobile (agent_id, amount, date_id) VALUES ($1, $2, $3)',
                                            [agentId, cleanAmount, date]
                                        );
                                        insertedCount++;
                                    }
                                }
                            }
                        } else {
                            console.log(`تحذير: لم يتم العثور على الوكيل ${agentName}`);
                        }
                    }
                    
                    console.log(`✓ تم استيراد ${insertedCount} سجل من بيانات ريال موبايل`);
                    resolve();
                } catch (err) {
                    console.error('خطأ في استيراد بيانات ريال موبايل:', err);
                    reject(err);
                }
            })
            .on('error', (err) => {
                console.error('خطأ في قراءة ملف ريال موبايل:', err);
                reject(err);
            });
    });
}

async function main() {
    try {
        console.log('بدء عملية استيراد البيانات...');
        
        // استيراد بيانات البوابة
        await importGatewayData();
        
        // استيراد بيانات ريال موبايل
        await importRyalData();
        
        console.log('✓ تم استيراد جميع البيانات بنجاح!');
        
    } catch (err) {
        console.error('خطأ في عملية الاستيراد:', err);
    } finally {
        await pool.end();
    }
}

// تشغيل الاستيراد
main();
